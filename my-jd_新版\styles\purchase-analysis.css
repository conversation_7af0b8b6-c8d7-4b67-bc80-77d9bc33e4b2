/* purchase-analysis.css - 京东优惠分析悬浮窗样式 */

/* 主悬浮窗容器 */
#jd-promotion-floating-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 12px;
    width: 220px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    overflow: hidden;
}

/* 兼容旧版本的容器ID */
#purchase-analysis-root-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 12px;
}

/* 仪表板容器 */
#dashboard-container {
    width: 100%;
    background-color: white;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#dashboard-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 新版悬浮窗的仪表板样式 */
#jd-promotion-floating-panel #dashboard-container {
    border: 1px solid #007bff;
    border-radius: 8px;
}

/* 仪表板头部 */
.dashboard-header {
    background-color: #007bff;
    color: white;
    padding: 8px;
    font-weight: bold;
    cursor: grab;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.dashboard-header:active {
    cursor: grabbing;
}

.dashboard-content {
    padding: 5px;
}

.dashboard-item {
    display: flex;
    justify-content: space-between;
    padding: 3px 5px;
    border-bottom: 1px solid #eee;
}

.dashboard-item:last-child {
    border-bottom: none;
}

.dashboard-label {
    color: #333;
}

.dashboard-value {
    font-weight: bold;
    color: #ff4400;
}

/* 详细分析面板 - 新版悬浮窗 */
#purchase-analysis-container {
    position: fixed;
    top: 20px;
    right: 250px;
    width: 600px;
    height: 500px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
    display: none;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 12px;
}

/* 兼容旧版本样式 */
#jd-promotion-floating-panel #purchase-analysis-container {
    width: 450px;
    position: relative;
    top: auto;
    right: auto;
    height: auto;
}

#purchase-analysis-container .header {
    background-color: #f5f5f5;
    padding: 5px 10px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#purchase-analysis-container .header button {
    margin-left: 5px;
}

#close-analysis-btn {
    border: none;
    background: none;
    font-size: 16px;
    cursor: pointer;
}

#purchase-analysis-container .content {
    padding: 10px;
}

#purchase-analysis-container .summary {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#purchase-analysis-container .summary-item p {
    margin: 0;
}

#purchase-analysis-container .summary-item p:first-child {
    color: #888;
}

#purchase-analysis-container .sub-summary {
    padding: 5px 0;
    color: #888;
}

#purchase-analysis-container .promotions ul {
    list-style-type: none;
    padding-left: 0;
}

#purchase-analysis-container .promotions li {
    margin-bottom: 5px;
}

#purchase-analysis-container .promotions li span {
    margin-right: 10px;
}

#purchase-analysis-container .promotions li button {
    margin-left: 5px;
}

#purchase-analysis-container .details .tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
}

#purchase-analysis-container .details .tab-link {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 10px 15px;
    transition: 0.3s;
}

#purchase-analysis-container .details .tab-link.active {
    background-color: #ddd;
    font-weight: bold;
}

#purchase-analysis-container .details .switch-view button.active {
    background-color: #ddd;
}

#purchase-analysis-container .details .tab-content {
    display: block;
    /* Always show */
    padding: 10px 0;
}

#purchase-analysis-container .details table {
    width: 100%;
    border-collapse: collapse;
}

#purchase-analysis-container .details th,
#purchase-analysis-container .details td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

#purchase-analysis-container .details th {
    background-color: #f2f2f2;
}

#purchase-analysis-container .details .optimal {
    background-color: #ff4400;
    color: white;
    border: none;
    padding: 2px 5px;
}

#purchase-analysis-container .details .sub-optimal {
    background-color: #ff8c00;
    color: white;
    border: none;
    padding: 2px 5px;
}

#purchase-analysis-container.hidden {
    display: none;
}

#purchase-analysis-container .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
}

#purchase-analysis-container .pagination a,
#purchase-analysis-container .pagination span {
    margin: 0 5px;
}

#purchase-analysis-container .pagination select {
    margin-left: 10px;
}

/* 新版悬浮窗特有样式 */
#jd-promotion-floating-panel .dashboard-content {
    padding: 8px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

#jd-promotion-floating-panel .dashboard-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 11px;
}

#jd-promotion-floating-panel .dashboard-label {
    color: #6c757d;
    font-weight: normal;
}

#jd-promotion-floating-panel .dashboard-value {
    font-weight: bold;
    color: #28a745;
}

#jd-promotion-floating-panel .dashboard-actions {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

#jd-promotion-floating-panel .dashboard-actions button {
    padding: 4px 8px;
    font-size: 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#jd-promotion-floating-panel .dashboard-actions button:hover {
    opacity: 0.8;
}

/* 按钮样式 */
.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    #jd-promotion-floating-panel {
        width: 200px;
        font-size: 11px;
    }

    #purchase-analysis-container {
        width: 500px;
        right: 210px;
    }
}

@media (max-width: 900px) {
    #jd-promotion-floating-panel {
        width: 180px;
        font-size: 10px;
    }

    #purchase-analysis-container {
        width: 400px;
        right: 190px;
    }
}
