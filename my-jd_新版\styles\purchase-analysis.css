/* purchase-analysis.css */
#purchase-analysis-root-container {
    position: fixed;
    top: 120px;
    right: 20px;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    max-width: 700px;
    max-height: 80vh;
    overflow: visible;
}

#dashboard-container {
    position: relative;
    width: 220px;
    border: 2px solid #007bff;
    background-color: white;
    margin-bottom: 5px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: visible;
}

#dashboard-container:hover {
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
    transform: translateY(-2px);
}

.dashboard-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 12px 15px;
    font-weight: bold;
    font-size: 14px;
    text-align: left;
    letter-spacing: 0.5px;
}

.dashboard-content {
    padding: 0;
    background: white;
}

.dashboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 2px solid #f0f0f0;
    background: white;
    transition: background-color 0.2s ease;
}

.dashboard-item:hover {
    background: #f8f9ff;
}

.dashboard-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.dashboard-label {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.dashboard-value {
    /* font-weight: bold; */
    color: #ff4400;
    font-size: 20px;
    text-shadow: 0 1px 2px rgba(255, 68, 0, 0.1);
    letter-spacing: 0.3px;
}

#purchase-analysis-container {
    position: absolute;
    top: 100%;
    right: 0;
    width: 500px;
    height: 650px;
    border: 1px solid #ccc;
    background-color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    transform: translateX(100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 6px;
    margin-top: 5px;
    display: flex;
    flex-direction: column;
}

#purchase-analysis-container.show {
    transform: translateX(0);
    opacity: 1;
    visibility: visible;
}

#purchase-analysis-container .header {
    background-color: #f5f5f5;
    padding: 5px 10px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#purchase-analysis-container .header button {
    margin-left: 5px;
}

#close-analysis-btn {
    border: none;
    background: none;
    font-size: 16px;
    cursor: pointer;
}

#purchase-analysis-container .content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 10px;
    padding-bottom: 0; /* 移除底部内边距，为分页控件让出空间 */
}

#purchase-analysis-container .summary {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#purchase-analysis-container .summary-item p {
    margin: 0;
}

#purchase-analysis-container .summary-item p:first-child {
    color: #888;
}

#purchase-analysis-container .sub-summary {
    padding: 5px 0;
    color: #888;
}

#purchase-analysis-container .promotions ul {
    list-style-type: none;
    padding-left: 0;
}

#purchase-analysis-container .promotions li {
    margin-bottom: 5px;
}

#purchase-analysis-container .promotions li span {
    margin-right: 10px;
}

#purchase-analysis-container .promotions li button {
    margin-left: 5px;
}

#purchase-analysis-container .details .tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
}

#purchase-analysis-container .details .tab-link {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 10px 15px;
    transition: 0.3s;
}

#purchase-analysis-container .details .tab-link.active {
    background-color: #ddd;
    font-weight: bold;
}

#purchase-analysis-container .details .switch-view button.active {
    background-color: #ddd;
}

#purchase-analysis-container .details .tab-content {
    display: block;
    /* Always show */
    padding: 10px 0 0 0;
    flex: 1;
    overflow-y: auto;
    margin-bottom: 10px; /* 为分页控件预留空间 */
}

#purchase-analysis-container .details table {
    width: 100%;
    border-collapse: collapse;
}

#purchase-analysis-container .details th,
#purchase-analysis-container .details td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

#purchase-analysis-container .details th {
    background-color: #f2f2f2;
}

#purchase-analysis-container .details .optimal {
    background-color: #ff4400;
    color: white;
    border: none;
    padding: 2px 5px;
}

#purchase-analysis-container .details .sub-optimal {
    background-color: #ff8c00;
    color: white;
    border: none;
    padding: 2px 5px;
}

#purchase-analysis-container.hidden {
    display: none !important;
}

/* 分页控件独立于内容区域，固定在弹出窗体底部 */
#purchase-analysis-container .pagination-wrapper {
    flex-shrink: 0;
    background: white;
    border-top: 1px solid #eee;
    position: relative;
    z-index: 10;
    padding: 0;
}

/* 分页控件样式 - 固定在弹出窗体底部，独立于内容滚动 */
#purchase-analysis-container .pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: white;
    flex-wrap: wrap;
    gap: 8px;
    margin: 0;
    border-top: none;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    justify-content: center;
}

.pagination-btn {
    padding: 4px 8px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 3px;
    font-size: 12px;
    min-width: 28px;
    text-align: center;
}

.pagination-btn:hover:not(:disabled) {
    background: #f5f5f5;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.page-number {
    padding: 4px 8px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 3px;
    font-size: 12px;
    min-width: 28px;
    text-align: center;
    color: #333;
}

.page-number:hover:not(:disabled) {
    background: #f5f5f5;
}

.page-number.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.page-numbers {
    display: flex;
    gap: 3px;
    align-items: center;
    flex-wrap: nowrap;
    max-width: 200px;
    overflow: hidden;
}

.ellipsis {
    padding: 4px 2px;
    color: #999;
    font-size: 12px;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

#items-per-page {
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
    background: white;
    min-width: 80px;
}

#purchase-analysis-container .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 15px;
    background: white;
    flex-wrap: wrap;
    gap: 8px;
    margin: 0;
    border-top: none;
}

#purchase-analysis-container .pagination a,
#purchase-analysis-container .pagination span {
    margin: 0 5px;
}

#purchase-analysis-container .pagination select {
    margin-left: 10px;
}

/* 分页控件独立于内容区域，固定在弹出窗体底部 */
#purchase-analysis-container .pagination-wrapper {
    flex-shrink: 0; /* 不允许收缩 */
    background: white;
    border-top: 1px solid #eee;
    position: sticky; /* 使用sticky定位确保固定在底部 */
    bottom: 0;
    z-index: 10;
    margin-top: auto; /* 自动推到底部 */
}

/* 按钮通用样式 */
.btn {
    padding: 4px 8px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    margin: 2px;
    transition: all 0.2s ease;
}

.btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* 主要按钮样式 */
.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #ccc;
    color: #333;
}

/* 促销和优惠券样式 */
#purchase-analysis-container .promotions {
    margin: 10px 0;
}

#purchase-analysis-container .promotions p {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    font-size: 13px;
}

#purchase-analysis-container .promotions ul {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

#purchase-analysis-container .promotions li {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 1px 0;
    background: none;
    border: none;
    border-radius: 0;
    margin-bottom: 1px;
    transition: none;
    gap: 8px;
    flex-wrap: nowrap;
    min-height: 20px;
}

#purchase-analysis-container .promotions li:hover {
    background: none;
    border: none;
    transform: none;
    box-shadow: none;
}

#purchase-analysis-container .promotions li .promotion-amount {
    font-weight: bold;
    color: white;
    font-size: 13px;
    min-width: auto;
    flex-shrink: 0;
    background: linear-gradient(135deg, #ff4757, #ff3838);
    padding: 2px 6px;
    border-radius: 3px;
    text-align: center;
    box-shadow: 0 1px 2px rgba(255, 56, 56, 0.3);
    letter-spacing: 0.3px;
    line-height: 1.2;
    display: inline-block;
}

#purchase-analysis-container .promotions li .promotion-type {
    background-color: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    flex-shrink: 0;
    line-height: 1.2;
    min-width: auto;
    text-align: center;
}

#purchase-analysis-container .promotions li .coupon-type {
    background-color: #ff6600;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    flex-shrink: 0;
    line-height: 1.2;
    min-width: auto;
    text-align: center;
}

#purchase-analysis-container .promotions li .promotion-text {
    color: #333;
    font-size: 12px;
    flex: 1;
    min-width: 60px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#purchase-analysis-container .promotions li .promotion-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-shrink: 0;
}

#purchase-analysis-container .promotions li .promotion-actions .btn {
    background: none !important;
    border: none !important;
    color: #007bff !important;
    padding: 0 !important;
    border-radius: 0 !important;
    font-size: 12px !important;
    line-height: 1.3 !important;
    min-height: auto !important;
    white-space: nowrap !important;
    text-decoration: none !important;
    cursor: pointer !important;
    margin: 0 !important;
    transition: color 0.2s ease !important;
}

#purchase-analysis-container .promotions li .promotion-actions .btn:hover {
    color: #0056b3 !important;
    text-decoration: underline !important;
    background: none !important;
    transform: none !important;
    opacity: 1 !important;
}

/* 优惠券特殊样式 - 仅保留减免金额背景 */
#purchase-analysis-container .promotions li.coupon-item {
    background: none !important;
    border: none !important;
}

#purchase-analysis-container .promotions li.coupon-item:hover {
    background: none !important;
    border: none !important;
    transform: none;
    box-shadow: none;
}

#purchase-analysis-container .promotions li.coupon-item .promotion-amount {
    color: white !important;
    background: linear-gradient(135deg, #ff4757, #ff3838) !important;
    box-shadow: 0 1px 2px rgba(255, 56, 56, 0.3) !important;
}

/* 加车购买链接样式 */
.add-to-cart {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
    font-size: 12px;
}

.add-to-cart:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* 表格行选中样式 */
#purchase-analysis-container .details table tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#purchase-analysis-container .details table tr:hover {
    background-color: #f8f9fa;
}

#purchase-analysis-container .details table tr.selected {
    background-color: #e3f2fd;
    border-left: 3px solid #007bff;
}

#purchase-analysis-container .details table tr.selected:hover {
    background-color: #d1ecf1;
}

/* 操作按钮列样式 */
#purchase-analysis-container .details table .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    min-width: 180px;
}

#purchase-analysis-container .details table .optimal-badges {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

#purchase-analysis-container .details table .purchase-actions {
    display: flex;
    gap: 6px;
    margin-left: auto;
    flex-shrink: 0;
}

#purchase-analysis-container .details table .btn-cart {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#purchase-analysis-container .details table .btn-cart:hover {
    background-color: #218838;
}

#purchase-analysis-container .details table .btn-buy {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#purchase-analysis-container .details table .btn-buy:hover {
    background-color: #c82333;
}

/* 单选按钮样式 */
#purchase-analysis-container .details input[type="radio"] {
    margin-right: 8px;
}

/* 概要信息中的加车购买链接样式 */
#purchase-analysis-container .summary-item .add-to-cart-link,
#purchase-analysis-container .summary-item .buy-now-link {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
    margin: 0 4px;
    padding: 2px 4px;
    border-radius: 2px;
    transition: color 0.2s ease, background-color 0.2s ease;
}

#purchase-analysis-container .summary-item .add-to-cart-link:hover,
#purchase-analysis-container .summary-item .buy-now-link:hover {
    color: #0056b3;
    background-color: #f8f9fa;
    text-decoration: underline;
}

/* 概要信息样式增强 */
#purchase-analysis-container .summary-item {
    text-align: center;
    min-width: 120px;
}

#purchase-analysis-container .summary-item p:last-child {
    font-size: 14px;
    font-weight: bold;
}

#purchase-analysis-container .summary-item .price-highlight {
    color: #ff4400;
    font-size: 16px;
}

#purchase-analysis-container .summary-item .original-price {
    color: #999;
    text-decoration: line-through;
    font-size: 12px;
}

/* 标签切换样式 */
#purchase-analysis-container .details .switch-view {
    display: flex;
    align-items: center;
    gap: 5px;
}

#purchase-analysis-container .details .switch-view button {
    padding: 4px 12px;
    border: 1px solid #ccc;
    background-color: white;
    cursor: pointer;
    border-radius: 3px;
    font-size: 11px;
}

#purchase-analysis-container .details .switch-view button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* 滚动条样式 */
#purchase-analysis-root-container::-webkit-scrollbar {
    width: 6px;
}

#purchase-analysis-root-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#purchase-analysis-root-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#purchase-analysis-root-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

#purchase-analysis-root-container.show {
    animation: slideInFromRight 0.3s ease-out;
    transform: translateX(0);
}

/* 加载状态样式 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
}

.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 12px;
}

/* 成功状态样式 */
.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 12px;
}
