# 京东优惠分析悬浮窗功能说明

## 概述
京东优惠分析悬浮窗是一个集成了优惠计算、价格分析、文案生成等功能的浮动面板，位于京东商品页面的右上角，为用户提供实时的优惠信息和购买建议。

## 主要功能

### 1. 仪表板显示
- **状态指示器**: 显示模块加载状态（等待中/已就绪/计算中/完成）
- **最优价格**: 显示经过优惠计算后的最佳单价
- **最优数量**: 显示获得最佳价格的购买数量
- **节省金额**: 显示相比原价能节省的金额
- **单位换算**: 自动计算市斤价、单克价（适用于有重量信息的商品）

### 2. 详细分析面板
- **批量计算结果**: 显示1-200件商品的详细优惠分析
- **分页浏览**: 支持分页查看大量计算结果
- **数量选择**: 可选择特定数量查看详细信息
- **优惠明细**: 显示应用的具体优惠活动

### 3. 交互功能
- **复制文案**: 一键复制格式化的商品推广文案
- **发送功能**: 通过WebSocket发送文案到外部系统
- **加车购买**: 集成一键购买功能，支持指定数量购买
- **拖拽移动**: 支持拖拽调整悬浮窗位置

## 技术特性

### 模块集成
- **优惠算法模块**: 自动接收和处理优惠计算结果
- **格式化文案模块**: 集成现有的文案生成功能
- **WebSocket发送模块**: 支持远程发送功能
- **一键购买模块**: 集成购买流程

### 事件驱动架构
- 监听 `JdPromotionCalculated` 事件获取优惠数据
- 监听 `databaseFullUpdateComplete` 事件处理数据更新
- 支持模块间的松耦合通信

### 响应式设计
- 适配不同屏幕尺寸
- 自动调整字体大小和布局
- 保持良好的视觉效果

## 使用方法

### 基本操作
1. 打开京东商品页面，悬浮窗会自动出现在右上角
2. 等待优惠算法模块完成计算（状态显示为"计算完成"）
3. 查看仪表板中的最优价格和数量信息
4. 点击"详细分析"展开完整的分析面板

### 高级功能
1. **查看详细分析**: 点击"详细分析"按钮展开完整面板
2. **选择数量**: 在表格中点击任意行选择特定数量
3. **复制文案**: 点击"复制"按钮复制格式化文案
4. **发送文案**: 点击"发送"按钮通过WebSocket发送
5. **购买商品**: 点击"加车"按钮使用选中数量购买

### 拖拽功能
- 鼠标悬停在悬浮窗上会显示拖拽手柄
- 按住并拖拽可以移动悬浮窗位置
- 松开鼠标完成位置调整

## 文件结构

### 核心文件
- `js/京东优惠分析悬浮窗.js`: 主要逻辑文件
- `html/优惠信息展示.html`: HTML模板文件
- `styles/purchase-analysis.css`: 样式文件

### 依赖模块
- `js/优惠算法模块.js`: 优惠计算核心
- `js/复制格式化文案.js`: 文案生成模块
- `js/统一WebSocket发送_中间件版.js`: 发送功能
- `js/一键购买.js`: 购买功能

## 兼容性说明

### 浏览器支持
- Chrome 88+
- Edge 88+
- Firefox 85+

### 京东页面支持
- item.jd.com 商品页面
- jd.hk 港版商品页面
- npcitem.jd.hk 特殊商品页面

## 故障排除

### 常见问题
1. **悬浮窗不显示**: 检查扩展是否已启用，刷新页面重试
2. **数据不更新**: 等待优惠算法模块完成计算
3. **复制功能失效**: 检查浏览器剪贴板权限
4. **发送功能失败**: 检查网络连接和WebSocket服务状态

### 调试信息
- 打开浏览器开发者工具查看控制台日志
- 搜索 `[京东优惠分析悬浮窗]` 关键词查看相关日志
- 检查网络面板确认WebSocket连接状态

## 更新日志

### v2.0.0
- 重构为独立的悬浮窗组件
- 集成优惠算法模块
- 添加详细分析面板
- 支持拖拽移动
- 优化响应式设计
- 增强模块间兼容性
