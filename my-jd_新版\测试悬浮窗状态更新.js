// 京东优惠分析悬浮窗状态更新测试脚本
// 在浏览器控制台中运行此脚本来测试状态更新

console.log('🧪 开始测试悬浮窗状态更新...');

// 1. 检查悬浮窗实例
if (!window.jdPromotionFloatingPanel) {
    console.error('❌ 悬浮窗实例不存在');
} else {
    console.log('✅ 悬浮窗实例存在');
}

// 2. 检查关键DOM元素
const elementsToCheck = [
    'dashboard-status',
    'dashboard-best-price', 
    'dashboard-best-quantity',
    'dashboard-total-price',
    'dashboard-jin-price',
    'dashboard-gram-price',
    'best-price',
    'original-price',
    'best-quantity',
    'total-best-price',
    'total-original-price',
    'jin-price-display',
    'gram-price-display'
];

console.log('🔍 检查DOM元素:');
elementsToCheck.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: "${element.textContent}"`);
        if (element.textContent.includes('计算中') || element.textContent.includes('等待中')) {
            console.warn(`⚠️ ${id} 仍显示等待状态`);
        }
    } else {
        console.error(`❌ ${id}: 元素不存在`);
    }
});

// 3. 检查优惠算法模块
if (window.jdPromotionCalculator) {
    console.log('✅ 优惠算法模块存在');
} else {
    console.error('❌ 优惠算法模块不存在');
}

// 4. 检查现有优惠数据
if (window.currentPromotionData) {
    console.log('✅ 发现全局优惠数据:', window.currentPromotionData);
} else {
    console.log('ℹ️ 暂无全局优惠数据');
}

if (window.profitCalculator && window.profitCalculator.lastPromotionData) {
    console.log('✅ 发现毛利计算模块数据:', window.profitCalculator.lastPromotionData);
} else {
    console.log('ℹ️ 暂无毛利计算模块数据');
}

// 5. 手动触发数据更新测试
function testManualUpdate() {
    console.log('🔧 测试手动数据更新...');
    
    if (window.jdPromotionFloatingPanel) {
        // 检查现有数据
        window.jdPromotionFloatingPanel.checkExistingPromotionData();
        
        // 尝试手动计算
        setTimeout(() => {
            window.jdPromotionFloatingPanel.tryManualCalculation();
        }, 1000);
    }
}

// 6. 模拟优惠计算事件
function testPromotionEvent() {
    console.log('📊 测试优惠计算事件...');
    
    const testData = {
        source: 'manual_test',
        originalData: {
            wareName: '测试商品',
            price: { p: 19.9 }
        },
        results: {
            single: {
                finalUnitPrice: 14.57,
                quantity: 1,
                totalPrice: 14.57
            },
            optimal: {
                bestUnitPrice: 9.92,
                bestQuantity: 11,
                bestTotalPrice: 109.14,
                totalSavings: 44.65,
                appliedPromotions: [
                    { text: '买5件享9折', discount: 6.17 }
                ]
            }
        },
        timestamp: Date.now()
    };
    
    // 分发测试事件
    document.dispatchEvent(new CustomEvent('JdPromotionCalculated', {
        detail: testData
    }));
    
    console.log('📤 已发送测试事件');
}

// 7. 检查事件监听器
function checkEventListeners() {
    console.log('🎧 检查事件监听器...');
    
    // 尝试获取事件监听器信息（需要开发者工具支持）
    if (typeof getEventListeners === 'function') {
        const listeners = getEventListeners(document);
        if (listeners.JdPromotionCalculated) {
            console.log(`✅ JdPromotionCalculated 监听器数量: ${listeners.JdPromotionCalculated.length}`);
        } else {
            console.warn('⚠️ 未找到 JdPromotionCalculated 监听器');
        }
    } else {
        console.log('ℹ️ 无法检查事件监听器（需要开发者工具）');
    }
}

// 8. 综合测试函数
function runFullTest() {
    console.log('🚀 运行完整测试...');
    
    checkEventListeners();
    
    setTimeout(() => {
        testManualUpdate();
    }, 500);
    
    setTimeout(() => {
        testPromotionEvent();
    }, 1500);
    
    setTimeout(() => {
        console.log('📋 测试完成，检查上述输出结果');
        
        // 最终状态检查
        const statusElement = document.getElementById('dashboard-status');
        const priceElement = document.getElementById('dashboard-best-price');
        
        if (statusElement && !statusElement.textContent.includes('计算中') && !statusElement.textContent.includes('等待中')) {
            console.log('✅ 状态更新成功');
        } else {
            console.warn('⚠️ 状态仍未更新');
        }
        
        if (priceElement && !priceElement.textContent.includes('等待中') && !priceElement.textContent.includes('计算中')) {
            console.log('✅ 价格更新成功');
        } else {
            console.warn('⚠️ 价格仍未更新');
        }
    }, 3000);
}

// 导出测试函数到全局
window.testFloatingPanelStatus = {
    runFullTest,
    testManualUpdate,
    testPromotionEvent,
    checkEventListeners
};

console.log('🎯 测试脚本加载完成！');
console.log('💡 使用方法:');
console.log('  - window.testFloatingPanelStatus.runFullTest() // 运行完整测试');
console.log('  - window.testFloatingPanelStatus.testPromotionEvent() // 测试事件');
console.log('  - window.testFloatingPanelStatus.testManualUpdate() // 测试手动更新');

// 自动运行基础检查
setTimeout(() => {
    console.log('🔄 自动运行基础检查...');
    checkEventListeners();
}, 1000);
