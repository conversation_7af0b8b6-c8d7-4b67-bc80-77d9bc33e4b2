<!-- 京东优惠分析悬浮窗模板 - 用于动态注入到页面 -->
<!-- 此文件不再作为独立HTML页面使用，而是作为悬浮窗组件的模板 -->

<div id="jd-promotion-floating-panel" style="position: fixed; top: 20px; right: 20px; z-index: 10000; font-family: Arial, sans-serif; font-size: 12px; width: 220px;">
    <div id="dashboard-container">
        <div class="dashboard-header" style="background-color: #007bff; color: white; padding: 8px 12px; border-radius: 8px 8px 0 0; cursor: grab; user-select: none; display: flex; justify-content: space-between; align-items: center;">
            <span>京东优惠分析</span>
            <div>
                <button id="expand-panel-btn" style="background: none; border: none; color: white; cursor: pointer; margin-right: 8px;">📊</button>
                <button id="toggle-dashboard" style="background: none; border: none; color: white; cursor: pointer;">▼</button>
            </div>
        </div>
        <div class="dashboard-content" style="padding: 12px; background: white; border: 1px solid #007bff; border-top: none;">
            <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span class="dashboard-label" style="font-size: 12px; color: #666;">计算状态</span>
                <span class="dashboard-value" id="dashboard-status" style="font-size: 12px; font-weight: bold; color: #333;">初始化中...</span>
            </div>
            <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span class="dashboard-label" style="font-size: 12px; color: #666;">最优单价</span>
                <span class="dashboard-value" id="dashboard-best-price" style="font-size: 12px; font-weight: bold; color: #007bff;">等待中...</span>
            </div>
            <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span class="dashboard-label" style="font-size: 12px; color: #666;">推荐数量</span>
                <span class="dashboard-value" id="dashboard-best-quantity" style="font-size: 12px; font-weight: bold; color: #007bff;">等待中...</span>
            </div>
            <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span class="dashboard-label" style="font-size: 12px; color: #666;">最优总价</span>
                <span class="dashboard-value" id="dashboard-total-price" style="font-size: 12px; font-weight: bold; color: #28a745;">等待中...</span>
            </div>
            <!-- 单位换算信息 -->
            <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span class="dashboard-label" style="font-size: 12px; color: #666;">市斤价</span>
                <span class="dashboard-value" id="dashboard-jin-price" style="font-size: 12px; font-weight: bold; color: #6c757d;">计算中...</span>
            </div>
            <div class="dashboard-item" style="display: flex; justify-content: space-between;">
                <span class="dashboard-label" style="font-size: 12px; color: #666;">单克价</span>
                <span class="dashboard-value" id="dashboard-gram-price" style="font-size: 12px; font-weight: bold; color: #6c757d;">计算中...</span>
            </div>
        </div>
    </div>

    <!-- 详细分析面板 - 默认隐藏，点击展开按钮显示 -->
    <div id="purchase-analysis-container" style="display: none; width: 450px; border: 1px solid #ccc; background-color: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); margin-top: 5px;">
        <div class="header" style="background-color: #f5f5f5; padding: 8px 12px; font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
            <span>本商品购买分析报告</span>
            <div>
                <button id="copy-selected-content" style="margin-left: 5px; padding: 4px 8px; font-size: 12px;">复制选中文案</button>
                <button id="send-selected-content" style="margin-left: 5px; padding: 4px 8px; font-size: 12px;">发送选中文案</button>
                <button id="close-analysis-btn" style="border: none; background: none; font-size: 16px; cursor: pointer; margin-left: 10px;">✕</button>
            </div>
        </div>
        <div class="content" style="padding: 12px;">
            <div class="summary" style="display: flex; justify-content: space-around; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 10px;">
                <div class="summary-item">
                    <p style="margin: 0; color: #888; font-size: 12px;">最优单价/原价</p>
                    <p style="margin: 4px 0 0 0;"><span id="best-price">计算中...</span> / <span id="original-price">获取中...</span></p>
                </div>
                <div class="summary-item">
                    <p style="margin: 0; color: #888; font-size: 12px;">最优购买数</p>
                    <p style="margin: 4px 0 0 0;"><span id="best-quantity">计算中...</span> / <a href="#" id="add-to-cart-optimal" class="add-to-cart">加车购买</a></p>
                </div>
                <div class="summary-item">
                    <p style="margin: 0; color: #888; font-size: 12px;">最优总价/原价</p>
                    <p style="margin: 4px 0 0 0;"><span id="total-best-price">计算中...</span> / <span id="total-original-price">计算中...</span></p>
                </div>
            </div>
            <div class="sub-summary" style="padding: 5px 0; color: #888; font-size: 12px; text-align: center;">
                <span id="jin-price-display">市斤价: 计算中...</span>
                <span style="margin-left: 20px;" id="gram-price-display">单克价: 计算中...</span>
            </div>
            <!-- 优惠信息展示区域 -->
            <div class="promotions" style="margin-bottom: 15px;">
                <p style="margin: 0 0 8px 0; font-weight: bold; font-size: 13px;">参与促销&优惠券:</p>
                <ul id="promotion-list" style="list-style-type: none; padding-left: 0; margin: 0;">
                    <!-- 动态生成的优惠信息将插入这里 -->
                    <li style="margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                        <div style="color: #666;">正在加载优惠信息...</div>
                    </li>
                </ul>
            </div>
            <!-- 详细分析表格 -->
            <div class="details">
                <div class="tabs" style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ccc; padding-bottom: 5px; margin-bottom: 10px;">
                    <button class="tab-link active" style="background-color: #ddd; border: none; outline: none; cursor: pointer; padding: 8px 12px; font-size: 12px; font-weight: bold;">详细分析表</button>
                    <div class="switch-view">
                        <span style="font-size: 12px; color: #666;">切换:</span>
                        <button id="show-small-optimal" style="margin-left: 5px; padding: 4px 8px; font-size: 11px;">小优</button>
                        <button id="show-big-optimal" class="active" style="margin-left: 5px; padding: 4px 8px; font-size: 11px; background-color: #ddd;">大优</button>
                    </div>
                </div>
                <div id="details-table" class="tab-content" style="display: block; padding: 0;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                        <thead>
                            <tr>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">数量</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">到手单价</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">到手总计</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="analysis-table-body">
                            <!-- 动态生成的分析数据将插入这里 -->
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">
                                    <input type="radio" name="quantity-selection" value="1" style="margin-right: 5px;"> 1
                                </td>
                                <td style="border: 1px solid #ddd; padding: 8px;">计算中...</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">计算中...</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">
                                    <a href="#" class="add-to-cart-btn" data-quantity="1" style="font-size: 11px; text-decoration: none; color: #007bff;">加车购买</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="pagination" style="display: flex; justify-content: center; align-items: center; margin-top: 10px; font-size: 12px;">
                        <button id="prev-page" style="margin: 0 5px; padding: 4px 8px; font-size: 11px;">&lt;</button>
                        <span id="page-info">第 1 页</span>
                        <button id="next-page" style="margin: 0 5px; padding: 4px 8px; font-size: 11px;">&gt;</button>
                        <select id="page-size" style="margin-left: 10px; font-size: 11px;">
                            <option value="10">10条/页</option>
                            <option value="20">20条/页</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 注意：此文件现在是模板文件，不包含script标签，JavaScript逻辑在单独的JS文件中处理 -->
