<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>京东优惠分析</title>
    <link rel="stylesheet" href="../styles/purchase-analysis.css">
</head>
<!-- 京东优惠分析悬浮窗 - 集成优惠算法模块 -->
<body>
    <div id="purchase-analysis-root-container">
        <div id="dashboard-container">
            <div class="dashboard-header">
                <span>购买分析</span>
            </div>
            <div class="dashboard-content">
                <div class="dashboard-item">
                    <span class="dashboard-label">最优单件价</span>
                    <span class="dashboard-value" id="dashboard-best-price">¥12.22</span>
                </div>
                <div class="dashboard-item">
                    <span class="dashboard-label">最优购买数</span>
                    <span class="dashboard-value" id="dashboard-best-quantity">x5</span>
                </div>
                <div class="dashboard-item">
                    <span class="dashboard-label">最优到手价</span>
                    <span class="dashboard-value" id="dashboard-total-price">¥61.10</span>
                </div>
                <!-- 解析商品标题提取常见商品单位比如，g，kg，克，ml，升，毫升，米，件，套，个，箱，盒，袋，瓶，支，根，本等，g或者克换算成斤或者500g，ml或者毫升换算成升或者升，类似，自动显示解析的单位，及对应的换算单位。和价格。建议单独设置一个方法可以逐步完善添加，还有考虑比如当前标题乐肴居爆汁流沙包600g 20个应换算出最优单个价，最优市斤价，最优单克价，自动显示渲染 -->
                <div class="dashboard-item"></div>
                <div class="dashboard-item">
                    <span class="dashboard-label">根据标题应显示最优市斤价</span>
                    <span class="dashboard-value" id="dashboard--price">¥34.91</span>
                </div>
                <div class="dashboard-item">
                    <span class="dashboard-label">最优单克价</span>
                    <span class="dashboard-value" id="dashboard--price">¥0.07</span>
                </div>
            </div>
        </div>
        <div id="purchase-analysis-container">
            <div class="header">
                <span>本商品购买分析报告</span>
                <div>
                    <!-- 点击数量切换，自动复制对应的文案,文案内容就是算法模块提供的数据到手价单价总价，参考格式化文案模块，点击发送则使用websocket发送 -->
                    <button>复制对应数量文案</button>
                    <button>发送对应数量文案</button>
                    
                </div>
                <button id="close-analysis-btn">X</button>
            </div>
            <div class="content">
                <div class="summary">
                    <div class="summary-item">
                        <p>最优单价/原价</p>
                        <p><span id="best-price">¥12.22</span> / <span id="original-price">¥23.50</span></p>
                    </div>
                    <!-- 点击加车，根据数量构建加车，点击就加入购物车，点击购买，根据数量构建购买，点击就跳转到结算页面，参考一键购买模块 -->
                    <div class="summary-item">
                        <p>最优购买数</p>
                        <p><span id="best-quantity">×5</span> / <a href="#" class="add-to-cart">加车购买</a></p>
                    </div>
                    <div class="summary-item">
                        <p>最优总价/原价</p>
                        <p><span id="total-best-price">¥61.10</span> / <span
                                id="total-original-price">¥117.50</span>
                        </p>
                    </div>
                </div>
                <div class="sub-summary">
                    <span>市斤价:¥34.91</span>
                    <span>单克价:¥0.07</span>
                </div>
                <!-- 根据算法模块提供的数据渲染对应数量所需的优惠信息 
                 触屏优惠券的领取方式是https://coupon.m.jd.com/coupons/show.action?key=
            {这里是优惠券的key对应优惠券的encryptedKey字段}&
                 roleId={对应优惠券的roleId字段}&to=m.jd.com
                不管优惠券还是促销对应的方法下有toUrl字段，字段中的链接就是同优惠的凑单链接，点击去凑单就是对应的链接
                同品牌凑单构建方式https://search.jd.com/Search?activity_id=306982027910&exp_key={品牌名字  可以使用从wareInfoReadMap.cn_brand获取品牌信息
                const brandFromUnifiedData = this.getBrandFromUnifiedData();
                if (brandFromUnifiedData) {
                    categoryInfo.brand = brandFromUnifiedData;
                    console.log('📊 从wareInfoReadMap获取品牌:', categoryInfo.brand);
                }}

                 -->
                <div class="promotions">
                    <p>参与促销&优惠券:</p>
                    <ul id="promotion-list">
                        <li>
                            <span>减1.15元</span>
                            <span>PLUS专享立减</span>
                            <span>可与PLUS价、满减、券等优惠叠加使用</span>
                            <button>[忽略]</button>
                        </li>
                        <li>
                            <span>减35.25元</span>
                            <span>多买优惠</span>
                            <span>满2件，总价打8折；满3件，总价打7折</span>
                            <button>去凑单</button>
                            <button>同品牌凑单</button>
                            <button>[忽略]</button>
                        </li>
                        <li>
                            <span>减20元</span>
                            <span>粉|东券</span>
                            <span>满99减20</span>
                            <!--  -->
                            <button>触屏领取</button>
                            <button>去凑单</button>
                            <button>同品牌凑单</button>
                            <button>[忽略]</button>
                        </li>
                    </ul>
                </div>
                <!-- 最上边两条渲染小优，大优 ，没有大优则只显示一条小优，下边是数量依次排列，加车购买同上方的方法相同-->
                <div class="details">
                    <div class="tabs">
                        <button class="tab-link active">详细分析表</button>
                        <div class="switch-view">
                            切换:
                            <button>小优</button>
                            <button class="active">大优</button>
                        </div>
                    </div>
                    <div id="details-table" class="tab-content" style="display: block;">
                        <table>
                            <thead>
                                <tr>
                                    <th>数量</th>
                                    <th>到手单价</th>
                                    <th>到手总计</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="analysis-table-body">
                               <!--  <tr>
                                    <td><input type="radio" name="selection" checked> 200</td>
                                    <td>¥</td>
                                    <td>¥</td>
                                    <td><button class="optimal">大优</button> <a href="#">加车购买</a></td>
                                </tr> -->
                                <tr>
                                    <td><input type="radio" name="selection"> 5</td>
                                    <td>¥12.22</td>
                                    <td>¥61.10</td>
                                    <td><button class="sub-optimal">小优</button> <a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 1</td>
                                    <td>¥18.27</td>
                                    <td>¥18.27</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 2</td>
                                    <td>¥16.07</td>
                                    <td>¥32.14</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 3</td>
                                    <td>¥12.88</td>
                                    <td>¥38.66</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 4</td>
                                    <td>¥13.72</td>
                                    <td>¥54.88</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <!-- 第5条在第一行，所以这里是6 -->
                                <tr>
                                    <td><input type="radio" name="selection"> 6</td>
                                    <td>¥12.88</td>
                                    <td>¥77.32</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 7</td>
                                    <td>¥13.36</td>
                                    <td>¥93.54</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 8</td>
                                    <td>¥13.72</td>
                                    <td>¥109.76</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 9</td>
                                    <td>¥13.99</td>
                                    <td>¥125.98</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="selection"> 10</td>
                                    <td>¥14.22</td>
                                    <td>¥142.20</td>
                                    <td><a href="#">加车购买</a></td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="pagination">
                            <a href="#">&lt;</a>
                            <a href="#" class="active">1</a>
                            <a href="#">2</a>
                            <a href="#">3</a>
                            <a href="#">4</a>
                            <a href="#">5</a>
                            <span>...</span>
                            <a href="#">20</a>
                            <a href="#">&gt;</a>
                            <select>
                                <option>10条/页</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../js/优惠算法模块.js"></script>
   
</body>

</html>
