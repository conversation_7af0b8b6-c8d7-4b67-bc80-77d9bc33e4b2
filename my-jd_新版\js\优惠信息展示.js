/**
 * 优惠信息展示页面主要逻辑
 * 负责自动加载HTML并动态渲染优惠算法模块返回的数据
 * 
 * 交互设计：
 * - 默认只显示"购买分析"仪表板模块
 * - 鼠标悬停到仪表板时显示"本商品购买分析报告"详细模块
 * - 鼠标离开悬浮窗时隐藏详细模块
 * 
 * 外部依赖：
 * - window.calculateOptimalPurchase: 优惠算法模块的计算函数
 * - window.currentSkuId: 当前商品的SKU ID（由外部设置）
 * - window.currentProductPrice: 当前商品价格（由外部设置）
 * - window.currentBrand: 当前商品品牌（可选，由外部设置）
 * - window.addToCartWithQuantity: 一键购买模块的加车函数（可选）
 * - window.generateFormattedProductText: 格式化文案函数（可选）
 * - window.sendMessage: WebSocket发送函数（可选）
 * 
 * 外部接口：
 * - setAnalysisData(data): 设置分析数据并渲染
 * - refresh(): 手动触发重新渲染
 * - showAnalysis(): 显示整个悬浮窗
 * - hideAnalysis(): 隐藏整个悬浮窗
 * - showAnalysisDetails(): 显示详细分析报告
 * - hideAnalysisDetails(): 隐藏详细分析报告
 * - destroy(): 销毁悬浮窗
 */

class PurchaseAnalysisRenderer {
    constructor() {
        this.currentResults = null;
        this.selectedQuantityIndex = 0;
        this.ignoredPromotions = new Set(); // 记录被忽略的促销
        this.ignoredCoupons = new Set(); // 记录被忽略的优惠券
        this.containerElement = null; // 悬浮窗容器元素
        this.isLoaded = false; // 标记是否已加载
        this.currentPage = 1; // 当前页码
        this.itemsPerPage = 10; // 每页显示条数

        // 鼠标悬停状态跟踪
        this.isHoveringDashboard = false;
        this.isHoveringAnalysis = false;
        this.preventHide = false;

        // 悬停计时器
        this.hoverTimer = null;

        // URL和站点信息
        this.currentPageUrl = null; // 当前页面URL（去掉html后的内容）
        this.isInternationalSite = false; // 是否为国际站
        this.currentSkuId = null; // 当前商品SKU ID
    }

    /**
     * 初始化 - 自动加载HTML并启动
     */
    async init() {
        try {
            // 提取和处理当前页面URL
            this.extractCurrentPageInfo();

            // 检查是否在京东商品页面
            if (!this.isJDProductPage()) {
                console.log('不在京东商品页面，跳过购买分析初始化');
                return;
            }

            // 避免重复初始化
            if (this.isLoaded || document.getElementById('purchase-analysis-root-container')) {
                console.log('购买分析已初始化，跳过重复加载');
                return;
            }

            console.log('开始加载购买分析悬浮窗...');
            await this.loadHTMLContent();
            this.bindEvents();
            this.bindDataEvents(); // 添加数据事件监听
            
            // 尝试加载已有的数据，如果没有则等待事件
            await this.loadDashboardData();
            
            this.isLoaded = true;
            console.log('购买分析悬浮窗加载完成');
        } catch (error) {
            console.error('购买分析初始化失败:', error);
        }
    }

    /**
     * 检查是否在京东商品页面
     */
    isJDProductPage() {
        // 基于处理后的URL和提取的信息判断
        if (!this.currentPageUrl || !this.currentSkuId) {
            return false;
        }
        
        // 检查是否为京东商品页面URL格式
        if (this.isInternationalSite) {
            // 国际站格式: https://npcitem.jd.hk/数字.html
            return this.currentPageUrl.includes('npcitem.jd.hk') && /\/\d+\.html$/.test(this.currentPageUrl);
        } else {
            // 中文站格式: https://item.jd.com/数字.html
            return this.currentPageUrl.includes('item.jd.com') && /\/\d+\.html$/.test(this.currentPageUrl);
        }
    }

    /**
     * 获取HTML模板内容
     */
    getHTMLTemplate() {
        return `
            <div id="purchase-analysis-root-container">
                <div id="dashboard-container">
                    <div class="dashboard-header">
                        <span>购买分析</span>
                    </div>
                    <div class="dashboard-content">
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优单件价</span>
                            <span class="dashboard-value" id="dashboard-best-price">¥--</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优购买数</span>
                            <span class="dashboard-value" id="dashboard-best-quantity">x--</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优到手价</span>
                            <span class="dashboard-value" id="dashboard-total-price">¥--</span>
                        </div>
                    </div>
                </div>
                <div id="purchase-analysis-container">
                    <div class="header">
                        <span>本商品购买分析报告</span>
                        <div>
                            <button id="copy-selected-text-btn">复制对应数量文案</button>
                            <button id="send-selected-text-btn">发送对应数量文案</button>
                        </div>
                        <button id="close-analysis-btn">X</button>
                    </div>
                    <div class="content">
                        <div class="summary">
                            <div class="summary-item">
                                <p>最优单价/原价</p>
                                <p><span id="best-price" class="price-highlight">¥--</span> / <span id="original-price" class="original-price">¥--</span></p>
                            </div>
                            <div class="summary-item">
                                <p>最优购买数</p>
                                <p><span id="best-quantity">×--</span> / <a href="#" class="add-to-cart">加车购买</a></p>
                            </div>
                            <div class="summary-item">
                                <p>最优总价/原价</p>
                                <p><span id="total-best-price" class="price-highlight">¥--</span> / <span id="total-original-price" class="original-price">¥--</span></p>
                            </div>
                        </div>
                        <div class="promotions">
                            <p>参与促销&优惠券:</p>
                            <ul id="promotion-list">
                                <!-- 动态渲染促销和优惠券信息 -->
                            </ul>
                        </div>
                        <div class="details">
                            <div class="tabs">
                                <button class="tab-link active">详细分析表</button>
                                <div class="switch-view">
                                    切换:
                                    <button>小优</button>
                                    <button class="active">大优</button>
                                </div>
                            </div>
                            <div id="details-table" class="tab-content" style="display: block;">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>数量</th>
                                            <th>到手单价</th>
                                            <th>到手总计</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="analysis-table-body">
                                        <!-- 动态渲染分析表格数据 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- 分页控件固定在弹出窗口底部 -->
                    <div class="pagination-wrapper">
                        <div class="pagination">
                            <div class="pagination-controls">
                                <button id="prev-page" class="pagination-btn">&lt;</button>
                                <div id="page-numbers" class="page-numbers">
                                    <!-- 动态生成页码 -->
                                </div>
                                <button id="next-page" class="pagination-btn">&gt;</button>
                            </div>
                            <div class="pagination-info">
                                <select id="items-per-page">
                                    <option value="10" selected>10条/页</option>
                                    <option value="20">20条/页</option>
                                    <option value="50">50条/页</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载HTML内容到页面
     */
    async loadHTMLContent() {
        try {
            // 创建悬浮窗容器
            this.containerElement = document.createElement('div');
            this.containerElement.id = 'jd-purchase-analysis-floating-window';
            
            // 计算最佳位置
            const position = this.calculateOptimalPosition();
            
            this.containerElement.style.cssText = `
                position: fixed;
                top: ${position.top}px;
                right: ${position.right}px;
                z-index: 10000;
                width: 380px;
                max-height: 70vh;
                overflow-y: auto;
                overflow-x: hidden;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;
            
            // 插入HTML模板内容
            this.containerElement.innerHTML = this.getHTMLTemplate();
            
            // 根据站点类型选择插入点
            this.insertToPage();
            
            // 动态加载CSS样式
            await this.loadCSS();
            
            console.log('HTML内容加载完成');
        } catch (error) {
            console.error('加载HTML内容失败:', error);
            throw error;
        }
    }

    /**
     * 根据站点类型将容器插入到页面
     */
    insertToPage() {
        if (this.isInternationalSite) {
            // 国际站：插入到#track元素内的第一个位置
            const trackElement = document.querySelector("#track");
            if (trackElement) {
                trackElement.insertBefore(this.containerElement, trackElement.firstChild);
                console.log('已插入到国际站#track元素第一个位置');
            } else {
                console.error('国际站#track元素未找到');
                throw new Error('国际站插入点未找到');
            }
        } else {
            // 中文站：插入到body
            document.body.appendChild(this.containerElement);
            console.log('已插入到中文站body元素');
        }
    }

    /**
     * 动态加载CSS样式
     */
    async loadCSS() {
        try {
            // 检查CSS是否已加载
            if (document.getElementById('purchase-analysis-css')) {
                return;
            }

            // CSS样式由manifest.json的content_scripts已经注入，无需重复加载
            // 如果需要确保CSS加载，可以在这里添加检查逻辑
            console.log('CSS样式已通过manifest注入');
        } catch (error) {
            console.error('CSS样式检查失败:', error);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (!this.containerElement) return;

        // 关闭按钮
        const closeBtn = this.containerElement.querySelector('#close-analysis-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideAnalysisDetails());
        }

        // 仪表板鼠标悬停显示详细信息，离开隐藏
        const dashboardContainer = this.containerElement.querySelector('#dashboard-container');
        const analysisContainer = this.containerElement.querySelector('#purchase-analysis-container');

        if (dashboardContainer && analysisContainer) {
            // 改进仪表板悬停触发 - 使用多种事件提高灵敏度
            const showAnalysisWithDelay = () => {
                clearTimeout(this.hoverTimer);
                this.hoverTimer = setTimeout(() => {
                    console.log('🖱️ 延时触发：显示详细分析');
                    this.isHoveringDashboard = true;
                    this.showAnalysisDetails();
                }, 100); // 100ms延时，避免误触发
            };

            // 鼠标进入仪表板时显示详细分析
            dashboardContainer.addEventListener('mouseenter', () => {
                console.log('🖱️ 鼠标进入仪表板');
                this.isHoveringDashboard = true;
                this.showAnalysisDetails();
            });

            // 鼠标在仪表板上移动时也触发（提高灵敏度）
            dashboardContainer.addEventListener('mousemove', showAnalysisWithDelay);

            // 鼠标离开仪表板时标记状态
            dashboardContainer.addEventListener('mouseleave', (e) => {
                console.log('🖱️ 鼠标离开仪表板');
                clearTimeout(this.hoverTimer);
                this.isHoveringDashboard = false;

                // 检查是否移动到分析区域
                const analysisRect = analysisContainer.getBoundingClientRect();
                const mouseX = e.clientX;
                const mouseY = e.clientY;
                const buffer = 5;

                const movingToAnalysis = mouseX >= analysisRect.left - buffer &&
                                        mouseX <= analysisRect.right + buffer &&
                                        mouseY >= analysisRect.top - buffer &&
                                        mouseY <= analysisRect.bottom + buffer;

                if (!movingToAnalysis) {
                    // 延时检查是否需要隐藏
                    setTimeout(() => {
                        if (!this.isHoveringDashboard && !this.isHoveringAnalysis) {
                            console.log('🖱️ 延时检查：隐藏详细分析');
                            this.hideAnalysisDetails();
                        }
                    }, 150);
                }
            });

            // 鼠标进入详细分析区域时保持显示
            analysisContainer.addEventListener('mouseenter', () => {
                console.log('🖱️ 鼠标进入详细分析区域');
                this.isHoveringAnalysis = true;
                this.showAnalysisDetails();
            });

            // 鼠标离开详细分析区域时标记状态
            analysisContainer.addEventListener('mouseleave', () => {
                console.log('🖱️ 鼠标离开详细分析区域');
                this.isHoveringAnalysis = false;

                // 延时检查是否需要隐藏
                setTimeout(() => {
                    if (!this.isHoveringDashboard && !this.isHoveringAnalysis) {
                        console.log('🖱️ 延时检查：隐藏详细分析');
                        this.hideAnalysisDetails();
                    }
                }, 150);
            });

            // 鼠标离开整个悬浮窗容器时检查是否需要隐藏
            this.containerElement.addEventListener('mouseleave', (e) => {
                console.log('🖱️ 鼠标离开整个容器');
                this.handleMouseLeave(e);
            });
        }

        // 切换视图按钮
        const switchButtons = this.containerElement.querySelectorAll('.switch-view button');
        switchButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleViewSwitch(e));
        });

        // 表格行选择 - 使用事件委托
        this.containerElement.addEventListener('change', (e) => {
            if (e.target.name === 'selection') {
                this.handleQuantitySelection(e);
            } else if (e.target.id === 'items-per-page') {
                this.handleItemsPerPageChange(e);
            }
        });

        // 表格行点击选择 - 点击整行都可以切换选择
        this.containerElement.addEventListener('click', (e) => {
            // 处理加车和购买按钮点击
            if (e.target.dataset.action === 'addToCart') {
                e.stopPropagation();
                this.handleAddToCart(parseInt(e.target.dataset.quantity));
                return;
            }

            if (e.target.dataset.action === 'buyNow') {
                e.stopPropagation();
                this.handleBuyNow(parseInt(e.target.dataset.quantity));
                return;
            }

            // 处理行选择
            const row = e.target.closest('tr');
            if (row && row.querySelector('input[name="selection"]') && !e.target.closest('.purchase-actions')) {
                const radio = row.querySelector('input[name="selection"]');
                if (radio && !radio.checked) {
                    radio.checked = true;
                    // 触发change事件
                    const changeEvent = new Event('change', { bubbles: true });
                    radio.dispatchEvent(changeEvent);
                }
            }
        });

        // 防止下拉菜单交互时关闭弹窗
        this.containerElement.addEventListener('mousedown', (e) => {
            if (e.target.id === 'items-per-page' || e.target.closest('.pagination-info')) {
                // 阻止鼠标离开事件触发隐藏
                this.preventHide = true;
                setTimeout(() => {
                    this.preventHide = false;
                }, 500); // 500ms内不隐藏弹窗
            }
        });

        // 分页事件
        this.bindPaginationEvents();

        // 复制和发送按钮
        this.bindCopyAndSendButtons();

        // 添加拖拽功能
        this.makeDraggable();
    }

    /**
     * 处理鼠标离开事件，防止下拉菜单点击时关闭弹窗
     */
    handleMouseLeave(e) {
        console.log('🖱️ 处理鼠标离开事件', {
            preventHide: this.preventHide,
            isHoveringDashboard: this.isHoveringDashboard,
            isHoveringAnalysis: this.isHoveringAnalysis,
            clientX: e.clientX,
            clientY: e.clientY
        });

        // 如果设置了防止隐藏标志，则不处理
        if (this.preventHide) {
            console.log('🖱️ 防止隐藏标志已设置，不处理');
            return;
        }

        // 如果鼠标仍在仪表板或分析区域，不隐藏
        if (this.isHoveringDashboard || this.isHoveringAnalysis) {
            console.log('🖱️ 鼠标仍在仪表板或分析区域，不隐藏');
            return;
        }

        // 使用延时检查，给下拉菜单选项点击留出时间
        setTimeout(() => {
            // 再次检查所有条件
            if (this.preventHide || this.isHoveringDashboard || this.isHoveringAnalysis) {
                console.log('🖱️ 延时检查：条件不满足，不隐藏');
                return;
            }

            // 检查鼠标当前位置是否在弹窗区域内（仅在有效坐标时检查）
            if (e.clientX !== undefined && e.clientY !== undefined && e.clientX > 0 && e.clientY > 0) {
                const rect = this.containerElement.getBoundingClientRect();
                const mouseX = e.clientX;
                const mouseY = e.clientY;

                // 扩大检测区域，包含可能的下拉菜单
                const buffer = 30; // 30px缓冲区，给下拉菜单更多空间
                const isInBounds = mouseX >= rect.left - buffer &&
                                  mouseX <= rect.right + buffer &&
                                  mouseY >= rect.top - buffer &&
                                  mouseY <= rect.bottom + buffer;

                if (isInBounds) {
                    console.log('🖱️ 鼠标仍在扩展区域内，不隐藏');
                    return;
                }
            }

            // 检查是否有下拉菜单正在显示
            const selectElement = this.containerElement.querySelector('#items-per-page');
            const isSelectOpen = selectElement && (selectElement.matches(':focus') || selectElement.matches(':active'));

            if (isSelectOpen) {
                console.log('🖱️ 下拉菜单正在使用，不隐藏');
                return;
            }

            // 所有条件都满足，隐藏弹窗
            console.log('🖱️ 条件满足，隐藏详细分析');
            this.hideAnalysisDetails();
        }, 200); // 200ms延时，给用户更多时间
    }

    /**
     * 提取当前页面信息：URL、站点类型、SKU
     */
    extractCurrentPageInfo() {
        const fullUrl = window.location.href;
        
        // 提取当前页URL，去掉.html后的所有内容
        const htmlIndex = fullUrl.indexOf('.html');
        if (htmlIndex !== -1) {
            this.currentPageUrl = fullUrl.substring(0, htmlIndex + 5); // 保留.html
        } else {
            this.currentPageUrl = fullUrl;
        }
        
        // 判断是否为国际站
        this.isInternationalSite = this.currentPageUrl.includes('jd.hk');
        
        // 提取SKU ID
        this.currentSkuId = this.extractSkuFromUrl(this.currentPageUrl);
        
        console.log('当前页面信息:', {
            currentPageUrl: this.currentPageUrl,
            isInternationalSite: this.isInternationalSite,
            currentSkuId: this.currentSkuId
        });
    }

    /**
     * 从URL中提取SKU ID
     */
    extractSkuFromUrl(url) {
        try {
            // 匹配中文站和国际站的SKU模式
            // 中文站: https://item.jd.com/100159620918.html
            // 国际站: https://npcitem.jd.hk/100239979102.html
            const skuMatch = url.match(/\/(\d+)\.html/);
            if (skuMatch && skuMatch[1]) {
                return skuMatch[1];
            }
            return null;
        } catch (error) {
            console.error('提取SKU失败:', error);
            return null;
        }
    }

    /**
     * 绑定数据事件监听
     */
    bindDataEvents() {
        // 监听优惠算法模块的计算完成事件
        document.addEventListener('JdPromotionCalculated', (event) => {
            console.log('优惠信息展示模块接收到优惠算法数据:', event.detail);
            
            if (event.detail && event.detail.results) {
                // 保存完整的事件数据结构，保持与优惠算法模块的数据格式一致
                this.currentResults = event.detail;

                // 重置分页状态
                this.currentPage = 1;

                // 设置默认选择为第一行（小优）
                const optimalData = event.detail?.results?.optimal || event.detail?.optimal;
                const minQuantityData = optimalData?.minQuantityData;
                const batchData = event.detail?.results?.batch || event.detail?.batch;

                if (minQuantityData && batchData) {
                    // 找到小优数据在batch数组中的索引
                    this.selectedQuantityIndex = batchData.findIndex(item => item.quantity === minQuantityData.quantity);
                    if (this.selectedQuantityIndex === -1) {
                        this.selectedQuantityIndex = 0; // 如果找不到，默认选择第一个
                    }
                } else {
                    this.selectedQuantityIndex = 0;
                }
                
                // 立即渲染数据
                this.renderAllData();
                console.log('优惠信息展示模块数据渲染完成');
            }
        });

        // 监听页面数据更新事件（如果有其他数据源）
        document.addEventListener('ProductDataUpdated', (event) => {
            console.log('商品数据更新，重新加载优惠信息');
            this.loadDashboardData();
        });
    }

    /**
     * 加载分析数据
     */
    async loadAnalysisData() {
        try {
            // 从优惠算法模块获取数据
            if (typeof window.calculateOptimalPurchase === 'function') {
                // 由外部传入SKU和价格，或从其他模块获取
                const skuId = window.currentSkuId || null;
                const productPrice = window.currentProductPrice || null;
                
                if (skuId && productPrice) {
                    this.currentResults = await window.calculateOptimalPurchase(skuId, productPrice);
                    this.renderAllData();
                } else {
                    this.showError('无法获取商品信息，请确保SKU和价格已设置');
                }
            } else {
                this.showError('优惠算法模块未加载');
            }
        } catch (error) {
            console.error('加载分析数据失败:', error);
            this.showError('加载数据失败');
        }
    }

    /**
     * 加载完整分析数据并渲染（仪表板+详细分析）
     * 注意：详细分析数据会被渲染但默认隐藏，鼠标悬停时显示
     */
    async loadDashboardData() {
        try {
            // 首先检查是否已经有缓存的数据（来自事件监听）
            if (this.currentResults && this.currentResults.optimal) {
                console.log('使用已有的优惠算法数据进行渲染');
                this.renderAllData();
                return;
            }

            // 尝试从优惠算法模块直接获取数据
            if (typeof window.calculateOptimalPurchase === 'function') {
                console.log('调用优惠算法模块获取数据...');
                this.currentResults = await window.calculateOptimalPurchase();
                if (this.currentResults && this.currentResults.optimal) {
                    // 渲染全部数据，但详细分析容器保持隐藏状态
                    this.renderAllData();
                    console.log('完整分析数据加载完成');
                } else {
                    console.log('优惠算法模块暂无数据，等待数据事件...');
                }
            } else {
                console.log('优惠算法模块未加载，等待模块加载和数据事件...');
            }
        } catch (error) {
            console.error('加载完整分析数据失败:', error);
        }
    }

    /**
     * 加载完整分析数据（包括详细分析）
     */
    async loadFullAnalysisData() {
        try {
            // 从优惠算法模块获取数据
            if (typeof window.calculateOptimalPurchase === 'function') {
                // 由外部传入SKU和价格，或从其他模块获取
                const skuId = window.currentSkuId || null;
                const productPrice = window.currentProductPrice || null;
                
                if (skuId && productPrice) {
                    this.currentResults = await window.calculateOptimalPurchase(skuId, productPrice);
                    this.renderAllData();
                } else {
                    this.showError('无法获取商品信息，请确保SKU和价格已设置');
                }
            } else {
                this.showError('优惠算法模块未加载');
            }
        } catch (error) {
            console.error('加载分析数据失败:', error);
            this.showError('加载数据失败');
        }
    }

    /**
     * 渲染所有数据（仪表板 + 详细分析）
     */
    renderAllData() {
        if (!this.currentResults) return;

        this.renderDashboard();
        this.renderDetailsOnly();
    }

    /**
     * 仅渲染详细分析数据（不包括仪表板）
     */
    renderDetailsOnly() {
        if (!this.currentResults) return;

        this.renderSummary();
        this.renderPromotions();
        this.renderDetailsTable();
    }

    /**
     * 渲染仪表板
     */
    renderDashboard() {
        if (!this.currentResults) {
            console.log('renderDashboard: 没有数据可渲染');
            return;
        }

        // 尝试获取最优结果数据，优先从事件数据结构中获取
        let optimalData = null;
        
        if (this.currentResults.results && this.currentResults.results.optimal) {
            // 从事件监听获取的数据格式：event.detail.results.optimal
            optimalData = this.currentResults.results.optimal;
        } else if (this.currentResults.optimal) {
            // 从其他方式获取的数据格式
            optimalData = this.currentResults.optimal;
        } else if (this.currentResults.optimalUnitPrice) {
            // 直接从优惠算法模块获取的数据格式
            optimalData = this.currentResults;
        } else {
            console.log('renderDashboard: 无法解析最优数据格式');
            return;
        }

        console.log('renderDashboard: 渲染仪表板数据', optimalData);
        
        // 格式化价格，保留2位小数
        const bestPrice = optimalData.optimalUnitPrice || optimalData.finalUnitPrice;
        const totalPrice = optimalData.optimalTotalPrice || optimalData.finalPrice;
        
        this.updateElement('dashboard-best-price', bestPrice ? `¥${parseFloat(bestPrice).toFixed(2)}` : '¥--');
        this.updateElement('dashboard-best-quantity', `x${optimalData.optimalQuantity || optimalData.quantity || '--'}`);
        this.updateElement('dashboard-total-price', totalPrice ? `¥${parseFloat(totalPrice).toFixed(2)}` : '¥--');
    }

    /**
     * 渲染概要信息
     */
    renderSummary() {
        const selectedData = this.getSelectedQuantityData();
        if (!selectedData) return;

        // 格式化价格，保留2位小数
        this.updateElement('best-price', `¥${parseFloat(selectedData.finalUnitPrice || 0).toFixed(2)}`);
        this.updateElement('original-price', `¥${parseFloat(selectedData.originalUnitPrice || 0).toFixed(2)}`);
        this.updateElement('best-quantity', `×${selectedData.quantity || '--'}`);
        this.updateElement('total-best-price', `¥${parseFloat(selectedData.finalPrice || 0).toFixed(2)}`);
        this.updateElement('total-original-price', `¥${parseFloat(selectedData.originalTotalPrice || 0).toFixed(2)}`);

        // 更新加车购买链接
        this.updateAddToCartLinks(selectedData.quantity);
    }

    /**
     * 渲染促销和优惠券信息
     */
    renderPromotions() {
        const selectedData = this.getSelectedQuantityData();
        if (!selectedData) return;

        const promotionList = this.containerElement?.querySelector('#promotion-list');
        if (!promotionList) return;

        promotionList.innerHTML = '';

        // 渲染促销信息
        if (selectedData.appliedPromotions) {
            selectedData.appliedPromotions.forEach((promotion, index) => {
                if (!this.ignoredPromotions.has(`promotion_${index}`)) {
                    const li = this.createPromotionElement(promotion, 'promotion', index);
                    promotionList.appendChild(li);
                }
            });
        }

        // 渲染优惠券信息
        if (selectedData.appliedCoupons) {
            selectedData.appliedCoupons.forEach((coupon, index) => {
                if (!this.ignoredCoupons.has(`coupon_${index}`)) {
                    const li = this.createCouponElement(coupon, index);
                    promotionList.appendChild(li);
                }
            });
        }
    }

    /**
     * 创建促销元素
     */
    createPromotionElement(promotion, type, index) {
        const li = document.createElement('li');
        // 格式化促销金额，保留2位小数
        const discountAmount = parseFloat(promotion.discountAmount || 0).toFixed(2);
        li.innerHTML = `
            <span class="promotion-amount">-¥${discountAmount}</span>
            <span class="promotion-type">促销</span>
            <span class="promotion-text">${promotion.text || promotion.description}${promotion.method ? ' (' + promotion.method + ')' : ''}</span>
            <div class="promotion-actions">
                ${this.createPromotionButtons(promotion, type, index)}
            </div>
        `;
        return li;
    }

    /**
     * 创建优惠券元素
     */
    createCouponElement(coupon, index) {
        const li = document.createElement('li');
        li.className = 'coupon-item';
        // 格式化优惠券金额，保留2位小数
        const discountAmount = parseFloat(coupon.discountAmount || 0).toFixed(2);
        li.innerHTML = `
            <span class="promotion-amount">-¥${discountAmount}</span>
            <span class="coupon-type">券</span>
            <span class="promotion-text">${coupon.description || coupon.text || ''}</span>
            <div class="promotion-actions">
                ${this.createCouponButtons(coupon, index)}
            </div>
        `;
        return li;
    }

    /**
     * 创建促销按钮
     */
    createPromotionButtons(promotion, type, index) {
        let buttons = '';
        
        // 去凑单按钮
        if (promotion.raw && promotion.raw.toUrl) {
            buttons += `<button class="btn btn-primary" onclick="window.open('${promotion.raw.toUrl}', '_blank')">去凑单</button>`;
        }
        
        // 忽略按钮
        buttons += `<button class="btn btn-outline" onclick="purchaseAnalysis.ignorePromotion('${type}', ${index})">忽略</button>`;
        
        return buttons;
    }

    /**
     * 创建优惠券按钮
     */
    createCouponButtons(coupon, index) {
        let buttons = '';
        
        // 桌面端领取按钮（移除移动端触屏领取）
        
        // 去凑单按钮
        if (coupon.raw && coupon.raw.toUrl) {
            buttons += `<button class="btn btn-primary" onclick="window.open('${coupon.raw.toUrl}', '_blank')">去凑单</button>`;
        }
        
        // 忽略按钮
        buttons += `<button class="btn btn-outline" onclick="purchaseAnalysis.ignoreCoupon(${index})">忽略</button>`;
        
        return buttons;
    }

    /**
     * 渲染详细分析表
     */
    renderDetailsTable() {
        // 从优惠算法模块返回的数据结构中获取batch数组
        const batchData = this.currentResults?.results?.batch || this.currentResults?.batch;
        if (!batchData) {
            console.log('renderDetailsTable: 没有批量数据可渲染');
            return;
        }

        const tableBody = this.containerElement?.querySelector('#analysis-table-body');
        if (!tableBody) return;

        // 获取小优和大优数据
        const optimalData = this.currentResults?.results?.optimal || this.currentResults?.optimal;
        const minQuantityData = optimalData?.minQuantityData;
        const maxQuantityData = optimalData?.maxQuantityData;

        // 清空表格
        tableBody.innerHTML = '';

        // 如果是第一页，显示小优和大优逻辑
        if (this.currentPage === 1) {
            // 构建要显示的行数据
            const displayRows = [];
            const usedQuantities = new Set();

            // 第一行：小优（必须显示）
            if (minQuantityData) {
                const minQuantityIndex = batchData.findIndex(item => item.quantity === minQuantityData.quantity);
                if (minQuantityIndex !== -1) {
                    displayRows.push({
                        data: batchData[minQuantityIndex], // 使用batch中的数据确保一致性
                        globalIndex: minQuantityIndex,
                        isMinQuantity: true,
                        isMaxQuantity: false
                    });
                    usedQuantities.add(minQuantityData.quantity);
                }
            }

            // 第二行：大优（如果存在且与小优不同）
            if (maxQuantityData && maxQuantityData.quantity !== minQuantityData?.quantity) {
                const maxQuantityIndex = batchData.findIndex(item => item.quantity === maxQuantityData.quantity);
                if (maxQuantityIndex !== -1) {
                    displayRows.push({
                        data: batchData[maxQuantityIndex], // 使用batch中的数据确保一致性
                        globalIndex: maxQuantityIndex,
                        isMinQuantity: false,
                        isMaxQuantity: true
                    });
                    usedQuantities.add(maxQuantityData.quantity);
                }
            }

            // 计算剩余可显示的行数
            const remainingSlots = this.itemsPerPage - displayRows.length;

            // 从数量1开始，跳过已使用的数量，填充剩余行
            let addedCount = 0;
            for (let quantity = 1; quantity <= batchData.length && addedCount < remainingSlots; quantity++) {
                if (!usedQuantities.has(quantity)) {
                    const dataIndex = batchData.findIndex(item => item.quantity === quantity);
                    if (dataIndex !== -1) {
                        displayRows.push({
                            data: batchData[dataIndex],
                            globalIndex: dataIndex,
                            isMinQuantity: false,
                            isMaxQuantity: false
                        });
                        addedCount++;
                    }
                }
            }

            // 渲染所有行
            displayRows.forEach(rowInfo => {
                const row = this.createTableRow(
                    rowInfo.data,
                    rowInfo.globalIndex,
                    rowInfo.isMinQuantity,
                    rowInfo.isMaxQuantity
                );
                tableBody.appendChild(row);
            });
        } else {
            // 其他页面使用正常分页逻辑
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = Math.min(startIndex + this.itemsPerPage, batchData.length);
            const currentPageData = batchData.slice(startIndex, endIndex);

            currentPageData.forEach((data, index) => {
                const globalIndex = startIndex + index;
                const row = this.createTableRow(data, globalIndex, false, false);
                tableBody.appendChild(row);
            });
        }

        // 计算分页信息（基于原始数据）
        const totalItems = batchData.length;
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        this.renderPagination(totalPages, totalItems);
    }

    /**
     * 创建表格行
     */
    createTableRow(data, globalIndex, isMinQuantity, isMaxQuantity) {
        const tr = document.createElement('tr');

        // 使用数据在batch数组中的实际索引来判断是否选中
        const isSelected = globalIndex === this.selectedQuantityIndex;
        if (isSelected) tr.classList.add('selected');

        // 为行添加点击样式
        tr.style.cursor = 'pointer';

        tr.innerHTML = `
            <td>
                <input type="radio" name="selection" value="${globalIndex + 1}" ${isSelected ? 'checked' : ''}>
                ${data.quantity}
            </td>
            <td>¥${parseFloat(data.finalUnitPrice || 0).toFixed(2)}</td>
            <td>¥${parseFloat(data.finalPrice || 0).toFixed(2)}</td>
            <td class="action-buttons">
                <div class="optimal-badges">
                    ${isMinQuantity ? '<button class="btn sub-optimal">小优</button>' : ''}
                    ${isMaxQuantity ? '<button class="btn optimal">大优</button>' : ''}
                </div>
                <div class="purchase-actions">
                    <a href="#" class="cart-link" data-quantity="${data.quantity}" data-action="addToCart">加车</a>
                    &nbsp;&nbsp;
                    <a href="#" class="buy-link" data-quantity="${data.quantity}" data-action="buyNow">购买</a>
                </div>
            </td>
        `;

        return tr;
    }

    /**
     * 处理数量选择
     */
    handleQuantitySelection(event) {
        const globalIndex = parseInt(event.target.value) - 1; // 转换为0基索引
        this.selectedQuantityIndex = globalIndex;
        this.renderSummary();
        this.renderPromotions();
        this.updateTableSelection();
    }

    /**
     * 更新表格选择状态
     */
    updateTableSelection() {
        const rows = this.containerElement?.querySelectorAll('#analysis-table-body tr') || [];

        rows.forEach((row) => {
            const radio = row.querySelector('input[type="radio"]');
            if (radio) {
                const radioValue = parseInt(radio.value) - 1; // 转换为0基索引

                if (radioValue === this.selectedQuantityIndex) {
                    row.classList.add('selected');
                    radio.checked = true;
                } else {
                    row.classList.remove('selected');
                    radio.checked = false;
                }
            }
        });
    }

    /**
     * 处理视图切换
     */
    handleViewSwitch(event) {
        const buttons = document.querySelectorAll('.switch-view button');
        buttons.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        // 这里可以添加切换小优/大优显示的逻辑
    }

    /**
     * 忽略促销
     */
    ignorePromotion(type, index) {
        this.ignoredPromotions.add(`${type}_${index}`);
        this.recalculateWithIgnored();
        this.renderPromotions();
        this.renderSummary();
    }

    /**
     * 忽略优惠券
     */
    ignoreCoupon(index) {
        this.ignoredCoupons.add(`coupon_${index}`);
        this.recalculateWithIgnored();
        this.renderPromotions();
        this.renderSummary();
    }

    /**
     * 重新计算忽略促销后的价格
     */
    recalculateWithIgnored() {
        // 这里需要重新计算忽略某些促销后的价格
        // 实现逻辑：总优惠金额减去被忽略的优惠金额，重新计算单价和总价
        const selectedData = this.getSelectedQuantityData();
        if (!selectedData) return;

        let ignoredAmount = 0;
        
        // 计算被忽略的促销金额
        selectedData.appliedPromotions?.forEach((promotion, index) => {
            if (this.ignoredPromotions.has(`promotion_${index}`)) {
                ignoredAmount += parseFloat(promotion.discountAmount) || 0;
            }
        });

        // 计算被忽略的优惠券金额
        selectedData.appliedCoupons?.forEach((coupon, index) => {
            if (this.ignoredCoupons.has(`coupon_${index}`)) {
                ignoredAmount += parseFloat(coupon.discountAmount) || 0;
            }
        });

        // 重新计算价格
        const newFinalPrice = selectedData.originalTotalPrice - (selectedData.totalDiscount - ignoredAmount);
        const newFinalUnitPrice = newFinalPrice / selectedData.quantity;

        // 更新选中数据
        selectedData.finalPrice = newFinalPrice.toFixed(2);
        selectedData.finalUnitPrice = newFinalUnitPrice.toFixed(2);
    }

    /**
     * 获取当前选中的数量数据
     */
    getSelectedQuantityData() {
        if (!this.currentResults) return null;
        
        // 从优惠算法模块的数据结构中获取batch数组
        const batchData = this.currentResults?.results?.batch || this.currentResults?.batch;
        const optimalData = this.currentResults?.results?.optimal || this.currentResults?.optimal;
        
        // 优先从批量结果中获取选中的数据
        if (batchData && batchData.length > 0) {
            const selectedData = batchData[this.selectedQuantityIndex] || batchData[0];
            return selectedData;
        }
        
        // 如果没有批量数据，返回最优数据
        if (optimalData) {
            return optimalData;
        }
        
        // 最后尝试返回任何可用数据
        return this.currentResults;
    }

    /**
     * 获取最优数量索引
     */
    getOptimalQuantityIndex() {
        if (!this.currentResults) return -1;
        
        const batchData = this.currentResults?.results?.batch || this.currentResults?.batch;
        const optimalData = this.currentResults?.results?.optimal || this.currentResults?.optimal;
        
        // 从批量数据中查找最优数量的索引
        if (batchData && optimalData && optimalData.optimalQuantity) {
            return batchData.findIndex(item => 
                item.quantity === optimalData.optimalQuantity
            );
        }
        
        return 0; // 默认返回第一个
    }

    /**
     * 更新加车购买链接
     */
    updateAddToCartLinks(quantity) {
        const addToCartLinks = this.containerElement?.querySelectorAll('.add-to-cart') || [];
        addToCartLinks.forEach(link => {
            const qty = link.dataset.quantity || quantity;
            link.onclick = (e) => {
                e.preventDefault();
                this.addToCart(qty);
            };
        });
    }

    /**
     * 处理加车操作
     */
    handleAddToCart(quantity) {
        try {
            const productId = this.getProductId();
            if (!productId) {
                alert('无法获取商品ID，请刷新页面重试');
                return;
            }

            console.log(`优惠信息展示: 加车 - 商品ID: ${productId}, 数量: ${quantity}`);

            // 构建加入购物车URL
            const timestamp = Date.now();
            const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;

            // 使用Image对象快速发送请求
            const img = new Image();
            img.onload = img.onerror = () => {
                console.log('优惠信息展示: 加入购物车请求已发送');
                this.showSuccess(`已加入购物车 ${quantity} 件`);
            };
            img.src = addToCartUrl;

        } catch (error) {
            console.error('加车失败:', error);
            alert('加车失败，请手动操作');
        }
    }

    /**
     * 处理购买操作
     */
    handleBuyNow(quantity) {
        try {
            const productId = this.getProductId();
            if (!productId) {
                alert('无法获取商品ID，请刷新页面重试');
                return;
            }

            console.log(`优惠信息展示: 购买 - 商品ID: ${productId}, 数量: ${quantity}`);

            // 构建加入购物车URL
            const timestamp = Date.now();
            const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;
            const orderUrl = 'https://trade.jd.com/shopping/order/getOrderInfo.action';

            // 使用Image对象快速发送请求
            const img = new Image();
            img.onload = img.onerror = () => {
                console.log('优惠信息展示: 加入购物车请求已发送');
            };
            img.src = addToCartUrl;

            // 立即打开订单页面
            setTimeout(() => {
                const orderWindow = window.open(orderUrl, '_blank');
                if (orderWindow) {
                    orderWindow.focus();
                    console.log('优惠信息展示: 购买流程完成，订单页面已打开');
                } else {
                    alert('无法打开新标签页，请检查浏览器弹窗拦截设置');
                }
            }, 200);

        } catch (error) {
            console.error('购买失败:', error);
            alert('购买失败，请手动操作');
        }
    }

    /**
     * 获取商品ID
     */
    getProductId() {
        try {
            // 从URL路径中提取SKU: /12345.html 或 /12345/
            const match = location.pathname.match(/\/(\d+)(?:\.html)?(?:\/|$)/);
            if (match && match[1]) {
                return match[1];
            }

            // 备用方案：从完整URL中提取
            const urlMatch = location.href.match(/\/(\d+)\.html/);
            if (urlMatch && urlMatch[1]) {
                return urlMatch[1];
            }

            // 兜底方案：从pageConfig获取
            if (window.pageConfig && window.pageConfig.product && window.pageConfig.product.skuid) {
                return window.pageConfig.product.skuid;
            }

            // 最后尝试从外部变量获取
            if (window.currentSkuId) {
                return window.currentSkuId;
            }

            return null;
        } catch (error) {
            console.error('优惠信息展示: 从URL提取SKU失败:', error);
            return null;
        }
    }

    /**
     * 加车购买（保留兼容性）
     */
    addToCart(quantity) {
        this.handleAddToCart(quantity);
    }

    /**
     * 绑定复制和发送按钮
     */
    bindCopyAndSendButtons() {
        if (!this.containerElement) return;

        // 复制文案按钮
        const copyBtn = this.containerElement.querySelector('#copy-selected-text-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.copyFormattedText();
            });
        }

        // 发送文案按钮
        const sendBtn = this.containerElement.querySelector('#send-selected-text-btn');
        if (sendBtn) {
            sendBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.sendFormattedText();
            });
        }
    }

    /**
     * 复制格式化文案
     */
    copyFormattedText() {
        const selectedData = this.getSelectedQuantityData();
        if (!selectedData) return;

        // 调用格式化文案模块生成指定数量的文案
        let text = '';
        if (window.FormattedCopyManager && typeof window.FormattedCopyManager.generateFormattedContentWithQuantity === 'function') {
            text = window.FormattedCopyManager.generateFormattedContentWithQuantity(selectedData);
        } else if (typeof window.generateFormattedProductText === 'function') {
            // 备用方案：使用旧的格式化函数
            text = window.generateFormattedProductText(selectedData);
        } else {
            // 最后备用方案：简单格式化
            const unitPrice = parseFloat(selectedData.finalUnitPrice || 0).toFixed(2);
            const totalPrice = parseFloat(selectedData.finalPrice || 0).toFixed(2);
            text = `数量: ${selectedData.quantity}, 到手单价: ¥${unitPrice}, 到手总价: ¥${totalPrice}`;
        }

        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showSuccess('文案已复制到剪贴板');
            });
        } else {
            // 兼容性方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showSuccess('文案已复制到剪贴板');
        }
    }

    /**
     * 发送格式化文案
     */
    sendFormattedText() {
        const selectedData = this.getSelectedQuantityData();
        if (!selectedData) return;

        // 调用格式化文案模块生成指定数量的文案
        let text = '';
        if (window.FormattedCopyManager && typeof window.FormattedCopyManager.generateFormattedContentWithQuantity === 'function') {
            text = window.FormattedCopyManager.generateFormattedContentWithQuantity(selectedData);
        } else if (typeof window.generateFormattedProductText === 'function') {
            // 备用方案：使用旧的格式化函数
            text = window.generateFormattedProductText(selectedData);
        } else {
            // 最后备用方案：简单格式化
            const unitPrice = parseFloat(selectedData.finalUnitPrice || 0).toFixed(2);
            const totalPrice = parseFloat(selectedData.finalPrice || 0).toFixed(2);
            text = `数量: ${selectedData.quantity}, 到手单价: ¥${unitPrice}, 到手总价: ¥${totalPrice}`;
        }

        // 调用WebSocket发送功能
        if (typeof window.sendMessage === 'function') {
            window.sendMessage(text);
            this.showSuccess('文案已发送');
        } else {
            console.warn('WebSocket发送功能未找到');
            // 备用：复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showSuccess('WebSocket未找到，文案已复制到剪贴板');
                });
            }
        }
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        // 创建临时提示
        const toast = document.createElement('div');
        toast.className = 'success-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 50px;
            right: 20px;
            z-index: 10001;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    /**
     * 添加拖拽功能
     */
    makeDraggable() {
        if (!this.containerElement) return;

        const header = this.containerElement.querySelector('.dashboard-header') || 
                      this.containerElement.querySelector('#dashboard-container');
        
        if (!header) return;

        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.style.cursor = 'move';
        header.style.userSelect = 'none';

        const dragStart = (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
            }
        };

        const dragEnd = () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        };

        const drag = (e) => {
            if (isDragging) {
                e.preventDefault();
                
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                this.containerElement.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        };

        // 仅绑定鼠标事件（桌面端）
        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
    }

    /**
     * 显示详细分析报告
     */
    showAnalysisDetails() {
        if (!this.containerElement) return;

        const analysisContainer = this.containerElement.querySelector('#purchase-analysis-container');
        if (analysisContainer) {
            analysisContainer.classList.add('show');
            
            // 如果没有数据，尝试加载
            if (!this.currentResults) {
                this.loadDashboardData(); // 加载全部数据
            }
            // 如果已有数据，无需重新渲染，直接显示即可
        }
    }

    /**
     * 隐藏详细分析报告
     */
    hideAnalysisDetails() {
        if (!this.containerElement) return;

        const analysisContainer = this.containerElement.querySelector('#purchase-analysis-container');
        if (analysisContainer) {
            analysisContainer.classList.remove('show');
        }
    }

    /**
     * 隐藏整个分析窗口
     */
    hideAnalysis() {
        if (this.containerElement) {
            this.containerElement.style.display = 'none';
        }
    }

    /**
     * 显示整个分析窗口
     */
    showAnalysis() {
        if (this.containerElement) {
            this.containerElement.style.display = 'block';
        }
    }

    /**
     * 切换分析容器显示/隐藏（保留兼容性）
     */
    toggleAnalysisContainer() {
        if (!this.containerElement) return;

        const analysisContainer = this.containerElement.querySelector('#purchase-analysis-container');
        if (analysisContainer) {
            const isVisible = analysisContainer.style.display !== 'none';
            analysisContainer.style.display = isVisible ? 'none' : 'block';
        }
    }

    /**
     * 销毁悬浮窗
     */
    destroy() {
        // 清理计时器
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
            this.hoverTimer = null;
        }

        // 重置状态
        this.isHoveringDashboard = false;
        this.isHoveringAnalysis = false;
        this.preventHide = false;

        if (this.containerElement && this.containerElement.parentNode) {
            this.containerElement.parentNode.removeChild(this.containerElement);
        }

        // 移除CSS
        const cssLink = document.getElementById('purchase-analysis-css');
        if (cssLink && cssLink.parentNode) {
            cssLink.parentNode.removeChild(cssLink);
        }

        this.containerElement = null;
        this.isLoaded = false;
        
        // 重置页面信息
        this.currentPageUrl = null;
        this.isInternationalSite = false;
        this.currentSkuId = null;
    }

    /**
     * 更新元素内容
     */
    updateElement(id, content) {
        if (!this.containerElement) return;
        
        const element = this.containerElement.querySelector(`#${id}`);
        if (element) {
            element.textContent = content;
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        console.error('购买分析错误:', message);
        
        // 在悬浮窗中显示错误
        if (this.containerElement) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.cssText = `
                color: #ff4444;
                background: #fff2f2;
                border: 1px solid #ffcccc;
                padding: 10px;
                margin: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            errorDiv.textContent = message;
            
            // 插入到容器顶部
            this.containerElement.insertBefore(errorDiv, this.containerElement.firstChild);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);
        }
    }

    /**
     * 外部接口：设置分析数据并渲染
     */
    setAnalysisData(data) {
        if (!data) return;
        
        this.currentResults = data;
        this.renderAllData();
    }

    /**
     * 外部接口：手动触发重新渲染
     */
    refresh() {
        if (this.currentResults) {
            this.renderAllData();
        }
    }

    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        if (!this.containerElement) return;

        // 上一页按钮
        const prevBtn = this.containerElement.querySelector('#prev-page');
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.goToPrevPage());
        }

        // 下一页按钮
        const nextBtn = this.containerElement.querySelector('#next-page');
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.goToNextPage());
        }

        // 页码点击事件委托
        const pageNumbers = this.containerElement.querySelector('#page-numbers');
        if (pageNumbers) {
            pageNumbers.addEventListener('click', (e) => {
                if (e.target.classList.contains('page-number')) {
                    const pageNum = parseInt(e.target.textContent);
                    this.goToPage(pageNum);
                }
            });
        }
    }

    /**
     * 渲染分页控件
     */
    renderPagination(totalPages, totalItems) {
        const paginationContainer = this.containerElement?.querySelector('.pagination');
        const pageNumbers = this.containerElement?.querySelector('#page-numbers');
        const prevBtn = this.containerElement?.querySelector('#prev-page');
        const nextBtn = this.containerElement?.querySelector('#next-page');
        
        if (!pageNumbers || !prevBtn || !nextBtn || !paginationContainer) return;

        // 清空页码容器
        pageNumbers.innerHTML = '';

        // 更新上一页/下一页按钮状态
        prevBtn.disabled = this.currentPage === 1;
        nextBtn.disabled = this.currentPage === totalPages;

        // 如果只有一页，隐藏整个分页控件
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // 生成页码
        this.generatePageNumbers(totalPages, pageNumbers);

        // 添加总页数信息
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, totalItems);
        const infoElement = paginationContainer.querySelector('.pagination-info');
        if (infoElement) {
            // 在每页条数选择框前添加信息
            const existingInfo = infoElement.querySelector('.page-info');
            if (existingInfo) {
                existingInfo.remove();
            }
            
            const pageInfo = document.createElement('span');
            pageInfo.className = 'page-info';
            pageInfo.textContent = `${startItem}-${endItem} / ${totalItems}`;
            pageInfo.style.fontSize = '11px';
            pageInfo.style.color = '#666';
            pageInfo.style.marginRight = '8px';
            
            infoElement.insertBefore(pageInfo, infoElement.firstChild);
        }
    }

    /**
     * 生成页码数字
     */
    generatePageNumbers(totalPages, container) {
        const current = this.currentPage;
        const maxVisible = 5; // 减少到最多显示5个页码，避免溢出

        let startPage = Math.max(1, current - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        // 调整起始页，确保显示足够的页码
        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        // 如果起始页不是1，显示第一页和省略号
        if (startPage > 1) {
            this.createPageButton(container, 1, false);
            if (startPage > 2) {
                this.createEllipsis(container);
            }
        }

        // 显示页码范围
        for (let i = startPage; i <= endPage; i++) {
            this.createPageButton(container, i, i === current);
        }

        // 如果结束页不是最后一页，显示省略号和最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                this.createEllipsis(container);
            }
            this.createPageButton(container, totalPages, false);
        }
    }

    /**
     * 创建页码按钮
     */
    createPageButton(container, pageNum, isActive) {
        const button = document.createElement('button');
        button.className = `page-number ${isActive ? 'active' : ''}`;
        button.textContent = pageNum;
        button.disabled = isActive;
        container.appendChild(button);
    }

    /**
     * 创建省略号
     */
    createEllipsis(container) {
        const span = document.createElement('span');
        span.className = 'ellipsis';
        span.textContent = '...';
        container.appendChild(span);
    }

    /**
     * 跳转到指定页面
     */
    goToPage(pageNum) {
        const batchData = this.currentResults?.results?.batch || this.currentResults?.batch;
        if (!batchData) return;

        const totalPages = Math.ceil(batchData.length / this.itemsPerPage);
        
        if (pageNum >= 1 && pageNum <= totalPages) {
            this.currentPage = pageNum;
            this.renderDetailsTable();
        }
    }

    /**
     * 上一页
     */
    goToPrevPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }

    /**
     * 下一页
     */
    goToNextPage() {
        const batchData = this.currentResults?.results?.batch || this.currentResults?.batch;
        if (!batchData) return;

        const totalPages = Math.ceil(batchData.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }

    /**
     * 处理每页显示条数变化
     */
    handleItemsPerPageChange(event) {
        this.itemsPerPage = parseInt(event.target.value);
        this.currentPage = 1; // 重置到第一页
        this.renderDetailsTable();
    }

    /**
     * 计算最佳定位位置
     */
    calculateOptimalPosition() {
        try {
            if (this.isInternationalSite) {
                // 国际站：由于插入到#track内，使用相对定位
                return {
                    top: 20,
                    right: 20
                };
            } else {
                // 中文站：使用原有逻辑
                const productTitle = document.querySelector('body > div:nth-child(5) > div.w > div.product-intro.clearfix > div:nth-child(1) > div.sku-name');
                
                if (productTitle) {
                    const titleRect = productTitle.getBoundingClientRect();
                    // 将插件定位在商品标题的右侧，与标题顶部齐平
                    return {
                        top: Math.max(titleRect.top + window.scrollY, 120),
                        right: 20
                    };
                }
                
                // 如果找不到指定标题，使用默认位置
                return {
                    top: 120,
                    right: 20
                };
            }
        } catch (error) {
            console.warn('计算定位失败，使用默认位置:', error);
            return {
                top: 120,
                right: 20
            };
        }
    }
}

// 全局实例
let purchaseAnalysis;

// 自动初始化函数
async function initPurchaseAnalysis() {
    // 避免重复初始化
    if (purchaseAnalysis && purchaseAnalysis.isLoaded) {
        return;
    }

    try {
        purchaseAnalysis = new PurchaseAnalysisRenderer();
        await purchaseAnalysis.init();
    } catch (error) {
        console.error('购买分析初始化失败:', error);
    }
}

// 多种方式确保初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPurchaseAnalysis);
} else {
    // 如果页面已经加载完成，立即初始化
    initPurchaseAnalysis();
}

// 监听页面变化（适用于SPA页面）
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
        // 检测到URL变化或页面内容变化时重新初始化
        const currentUrl = window.location.href;
        if (currentUrl !== (window.lastCheckedUrl || '')) {
            window.lastCheckedUrl = currentUrl;
            setTimeout(initPurchaseAnalysis, 1000); // 延迟1秒确保页面稳定
        }
    });
    
    observer.observe(document.body, { 
        childList: true, 
        subtree: true 
    });
}

// 导出给其他模块使用
window.PurchaseAnalysisRenderer = PurchaseAnalysisRenderer;
window.initPurchaseAnalysis = initPurchaseAnalysis;
