<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页控件固定位置测试</title>
    <link rel="stylesheet" href="../styles/purchase-analysis.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-description {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-description h2 {
            color: #333;
            margin-top: 0;
        }
        
        .test-description ul {
            color: #666;
            line-height: 1.6;
        }
        
        .show-popup-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .show-popup-btn:hover {
            background: #0056b3;
        }
        
        /* 模拟大量数据的表格行 */
        .test-row {
            height: 40px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            padding: 0 12px;
        }
        
        .test-row:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-description">
            <h2>🧪 分页控件固定位置测试</h2>
            <p><strong>测试目标：</strong>验证分页控件是否固定在弹出窗口底部，不随内容滚动。</p>
            <ul>
                <li>✅ 分页控件应该固定在弹出窗口底部</li>
                <li>✅ 表格内容可以滚动，但分页控件保持固定</li>
                <li>✅ 分页控件不应该被内容遮挡</li>
                <li>✅ 分页控件应该有清晰的边界分隔</li>
                <li>🔧 点击下拉菜单选项时弹窗不应该关闭</li>
                <li>🖱️ 鼠标悬停在仪表盘上时弹窗应该一直显示</li>
                <li>🛠️ 使用F12开发者工具时鼠标移动不应导致弹窗关闭</li>
                <li>⚡ 仪表盘悬停触发应该更加灵敏</li>
                <li>🎯 点击表格任意位置都可以切换数量选择</li>
                <li>🏆 第一行和第二行固定显示小优和大优</li>
                <li>📊 下面的行从数量1开始，跳过已显示的数量</li>
            </ul>
        </div>
        
        <button class="show-popup-btn" onclick="showTestPopup()">显示测试弹出窗口</button>
        
        <!-- 测试用的弹出窗口结构 -->
        <div id="purchase-analysis-root-container" style="display: none;">
            <div id="dashboard-container">
                <div class="dashboard-header">
                    <span>京东优惠分析</span>
                    <div>
                        <button id="expand-panel-btn">📊</button>
                        <button id="toggle-dashboard">▼</button>
                    </div>
                </div>
                <div class="dashboard-content">
                    <div class="dashboard-item">
                        <span class="dashboard-label">计算状态</span>
                        <span class="dashboard-value">测试完成</span>
                    </div>
                </div>
            </div>
            
            <div id="purchase-analysis-container" class="show">
                <div class="header">
                    <span>📊 本商品购买分析报告</span>
                    <div>
                        <button id="copy-selected-text-btn">复制对应数量文案</button>
                        <button id="send-selected-text-btn">发送对应数量文案</button>
                    </div>
                    <button id="close-analysis-btn">×</button>
                </div>
                
                <div class="content">
                    <div class="summary">
                        <div class="summary-item">
                            <p>最优单价/原价</p>
                            <p><span>¥9.92</span> / <span>¥19.90</span></p>
                        </div>
                        <div class="summary-item">
                            <p>最优购买数</p>
                            <p><span>11件</span> / <a href="#" class="add-to-cart">加车购买</a></p>
                        </div>
                        <div class="summary-item">
                            <p>最优总价/原价</p>
                            <p><span>¥109.14</span> / <span>¥218.90</span></p>
                        </div>
                    </div>
                    
                    <div class="sub-summary">
                        <span>市斤价: ¥19.84</span>
                        <span style="margin-left: 20px;">单克价: ¥0.020</span>
                    </div>
                    
                    <div class="promotions">
                        <p>参与促销&优惠券:</p>
                        <ul>
                            <li>
                                <span class="promotion-amount">-¥6.17</span>
                                <span class="promotion-type">促销</span>
                                <span class="promotion-text">买5件享9折</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="details">
                        <div class="tabs">
                            <button class="tab-link active">详细分析表</button>
                            <div class="switch-view">
                                切换:
                                <button>小优</button>
                                <button class="active">大优</button>
                            </div>
                        </div>
                        
                        <div id="details-table" class="tab-content" style="display: block;">
                            <table>
                                <thead>
                                    <tr>
                                        <th>数量</th>
                                        <th>到手单价</th>
                                        <th>到手总计</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="analysis-table-body">
                                    <!-- 生成大量测试数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 分页控件固定在弹出窗口底部 -->
                <div class="pagination-wrapper">
                    <div class="pagination">
                        <div class="pagination-controls">
                            <button id="prev-page" class="pagination-btn">&lt;</button>
                            <div id="page-numbers" class="page-numbers">
                                <span class="page-number">1</span>
                                <span class="page-number active">2</span>
                                <span class="page-number">3</span>
                                <span class="ellipsis">...</span>
                                <span class="page-number">10</span>
                            </div>
                            <button id="next-page" class="pagination-btn">&gt;</button>
                        </div>
                        <div class="pagination-info">
                            <span class="page-info">显示 11-20 / 共 100 条</span>
                            <select id="items-per-page">
                                <option value="10" selected>10条/页</option>
                                <option value="20">20条/页</option>
                                <option value="50">50条/页</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTestPopup() {
            const container = document.getElementById('purchase-analysis-root-container');
            container.style.display = 'block';
            
            // 生成测试数据
            generateTestData();
            
            // 绑定关闭按钮
            document.getElementById('close-analysis-btn').onclick = function() {
                container.style.display = 'none';
            };

            // 绑定展开按钮
            document.getElementById('expand-panel-btn').onclick = function() {
                const analysisContainer = document.getElementById('purchase-analysis-container');
                analysisContainer.classList.toggle('show');
            };

            // 绑定鼠标离开事件测试
            container.addEventListener('mouseleave', (e) => {
                handleMouseLeave(e, container);
            });

            // 绑定下拉菜单防止关闭逻辑
            container.addEventListener('mousedown', (e) => {
                if (e.target.id === 'items-per-page' || e.target.closest('.pagination-info')) {
                    window.testPreventHide = true;
                    console.log('🔧 测试：下拉菜单交互，防止弹窗关闭');
                    setTimeout(() => {
                        window.testPreventHide = false;
                        console.log('🔧 测试：防止关闭标志已重置');
                    }, 500);
                }
            });

            // 绑定下拉菜单变化事件
            document.getElementById('items-per-page').addEventListener('change', function(e) {
                console.log('🔧 测试：下拉菜单选择了', e.target.value);
                console.log('✅ 测试通过：弹窗没有关闭');
            });

            // 绑定复制和发送按钮测试
            document.getElementById('copy-selected-text-btn').addEventListener('click', function(e) {
                e.stopPropagation();
                const selectedRow = document.querySelector('#analysis-table-body tr.selected');
                if (selectedRow) {
                    const quantity = selectedRow.querySelector('input[type="radio"]').value;
                    const unitPrice = selectedRow.children[1].textContent;
                    const totalPrice = selectedRow.children[2].textContent;
                    const text = `测试复制：数量 ${quantity}, 单价 ${unitPrice}, 总价 ${totalPrice}`;
                    console.log('📋 测试：复制选中数量文案:', text);
                    alert('测试：' + text);
                } else {
                    alert('测试：没有选中的行');
                }
            });

            document.getElementById('send-selected-text-btn').addEventListener('click', function(e) {
                e.stopPropagation();
                const selectedRow = document.querySelector('#analysis-table-body tr.selected');
                if (selectedRow) {
                    const quantity = selectedRow.querySelector('input[type="radio"]').value;
                    const unitPrice = selectedRow.children[1].textContent;
                    const totalPrice = selectedRow.children[2].textContent;
                    const text = `测试发送：数量 ${quantity}, 单价 ${unitPrice}, 总价 ${totalPrice}`;
                    console.log('📤 测试：发送选中数量文案:', text);
                    alert('测试：' + text);
                } else {
                    alert('测试：没有选中的行');
                }
            });
        }
        
        function generateTestData() {
            const tbody = document.getElementById('analysis-table-body');
            tbody.innerHTML = '';

            // 模拟小优和大优的显示逻辑
            const testData = [
                // 第一行：小优（数量3，单价最低）
                { quantity: 3, unitPrice: 0.46, totalPrice: 1.37, isMinQuantity: true, isMaxQuantity: false },
                // 第二行：大优（数量10，单价最低时的最大数量）
                { quantity: 10, unitPrice: 0.46, totalPrice: 4.56, isMinQuantity: false, isMaxQuantity: true },
                // 下面的行从数量1开始，跳过3和10
                { quantity: 1, unitPrice: 1.03, totalPrice: 1.03, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 2, unitPrice: 1.03, totalPrice: 2.05, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 4, unitPrice: 0.46, totalPrice: 1.82, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 5, unitPrice: 0.46, totalPrice: 2.28, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 6, unitPrice: 0.46, totalPrice: 2.74, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 7, unitPrice: 0.46, totalPrice: 3.19, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 8, unitPrice: 0.46, totalPrice: 3.65, isMinQuantity: false, isMaxQuantity: false },
                { quantity: 9, unitPrice: 0.46, totalPrice: 4.10, isMinQuantity: false, isMaxQuantity: false }
            ];

            testData.forEach((data, index) => {
                const row = document.createElement('tr');
                row.style.cursor = 'pointer';

                // 第一行默认选中
                if (index === 0) {
                    row.classList.add('selected');
                }

                row.innerHTML = `
                    <td>
                        <input type="radio" name="selection" value="${index + 1}" ${index === 0 ? 'checked' : ''}>
                        ${data.quantity}
                    </td>
                    <td>¥${data.unitPrice.toFixed(2)}</td>
                    <td>¥${data.totalPrice.toFixed(2)}</td>
                    <td class="action-buttons">
                        <div class="optimal-badges">
                            ${data.isMinQuantity ? '<button class="btn sub-optimal">小优</button>' : ''}
                            ${data.isMaxQuantity ? '<button class="btn optimal">大优</button>' : ''}
                        </div>
                        <div class="purchase-actions">
                            <button class="btn btn-cart" data-quantity="${data.quantity}" data-action="addToCart">加车</button>
                            <button class="btn btn-buy" data-quantity="${data.quantity}" data-action="buyNow">购买</button>
                        </div>
                    </td>
                `;

                // 添加行点击事件
                row.addEventListener('click', function(e) {
                    // 处理加车和购买按钮点击
                    if (e.target.dataset.action === 'addToCart') {
                        e.stopPropagation();
                        console.log('🛒 测试：加车按钮点击，数量:', data.quantity);
                        alert(`测试：加车 ${data.quantity} 件`);
                        return;
                    }

                    if (e.target.dataset.action === 'buyNow') {
                        e.stopPropagation();
                        console.log('💰 测试：购买按钮点击，数量:', data.quantity);
                        alert(`测试：购买 ${data.quantity} 件`);
                        return;
                    }

                    // 如果点击的不是购买操作区域
                    if (!e.target.closest('.purchase-actions')) {
                        const radio = row.querySelector('input[type="radio"]');
                        if (radio && !radio.checked) {
                            // 取消其他选中
                            tbody.querySelectorAll('tr').forEach(r => r.classList.remove('selected'));
                            tbody.querySelectorAll('input[type="radio"]').forEach(r => r.checked = false);

                            // 选中当前行
                            row.classList.add('selected');
                            radio.checked = true;

                            console.log('🎯 测试：点击行切换选择，当前选中数量:', data.quantity);
                        }
                    }
                });

                tbody.appendChild(row);
            });

            console.log('✅ 测试数据生成完成，包含小优和大优显示');
            console.log('🎯 测试：点击表格任意位置切换选择');
            console.log('🏆 测试：第一行显示小优，第二行显示大优');
            console.log('📊 测试：下面的行从数量1开始，跳过了3和10');
        }
        
        // 模拟优惠信息展示模块的鼠标离开处理逻辑
        function handleMouseLeave(e, containerElement) {
            // 模拟preventHide标志
            if (window.testPreventHide) {
                return;
            }

            setTimeout(() => {
                if (window.testPreventHide) {
                    return;
                }

                const rect = containerElement.getBoundingClientRect();
                const mouseX = e.clientX;
                const mouseY = e.clientY;

                const buffer = 20;
                const isInBounds = mouseX >= rect.left - buffer &&
                                  mouseX <= rect.right + buffer &&
                                  mouseY >= rect.top - buffer &&
                                  mouseY <= rect.bottom + buffer;

                const selectElement = containerElement.querySelector('#items-per-page');
                const isSelectOpen = selectElement && (selectElement.matches(':focus') || selectElement.matches(':active'));

                if (!isInBounds && !isSelectOpen) {
                    containerElement.style.display = 'none';
                    console.log('🔍 测试：弹窗已隐藏');
                }
            }, 150);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 分页固定位置测试页面已加载');
            console.log('💡 点击"显示测试弹出窗口"按钮开始测试');
            console.log('🔧 测试下拉菜单：点击"条/页"下拉菜单，选择不同选项，观察弹窗是否保持打开');
        });
    </script>
</body>
</html>
