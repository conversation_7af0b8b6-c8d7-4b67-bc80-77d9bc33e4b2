# 京东优惠分析悬浮窗事件调试指南

## 问题现象
- 优惠算法模块已经计算完成并发送了 `JdPromotionCalculated` 事件
- 悬浮窗显示"等待中"和"计算中"状态，没有接收到事件

## 调试步骤

### 1. 检查事件监听器是否正确设置
在浏览器控制台中运行：
```javascript
// 检查悬浮窗实例是否存在
console.log('悬浮窗实例:', window.jdPromotionFloatingPanel);

// 检查事件监听器数量
console.log('JdPromotionCalculated事件监听器数量:', 
  document.getEventListeners ? 
  document.getEventListeners(document)['JdPromotionCalculated']?.length : 
  '无法检查（需要开发者工具）');
```

### 2. 手动触发事件测试
```javascript
// 手动创建并分发测试事件
const testEvent = new CustomEvent('JdPromotionCalculated', {
  detail: {
    source: 'manual_test',
    results: {
      single: { finalUnitPrice: 10.5, quantity: 1 },
      optimal: { bestUnitPrice: 9.5, bestQuantity: 5 }
    }
  }
});
document.dispatchEvent(testEvent);
```

### 3. 检查现有优惠数据
```javascript
// 检查全局优惠数据
console.log('全局优惠数据:', window.currentPromotionData);
console.log('毛利计算模块数据:', window.profitCalculator?.lastPromotionData);
console.log('优惠算法模块实例:', window.jdPromotionCalculator);
```

### 4. 使用手动计算按钮
- 点击悬浮窗右上角的 🔧 按钮
- 观察控制台日志输出
- 检查是否能成功触发计算

### 5. 检查模块加载顺序
```javascript
// 检查各模块的加载状态
console.log('模块加载状态:', {
  优惠算法模块: !!window.jdPromotionCalculator,
  悬浮窗模块: !!window.jdPromotionFloatingPanel,
  毛利计算模块: !!window.profitCalculator,
  数据库交互模块: !!window.databaseManager
});
```

## 修复方案

### 1. 增强事件监听
- 添加了事件监听器设置的日志
- 增加了现有数据检查机制
- 添加了延迟检查功能

### 2. 手动计算功能
- 添加了手动触发计算的按钮（🔧）
- 实现了从页面提取基础数据的功能
- 提供了备用计算方案

### 3. 调试信息增强
- 增加了详细的日志输出
- 添加了状态更新提示
- 提供了错误处理机制

## 常见问题排查

### 问题1: 事件监听器未设置
**症状**: 控制台没有"设置事件监听器"的日志
**解决**: 检查悬浮窗初始化是否完成

### 问题2: 事件发送时机问题
**症状**: 优惠计算在悬浮窗初始化之前完成
**解决**: 使用延迟检查和现有数据检查机制

### 问题3: 数据格式不匹配
**症状**: 事件接收到但数据处理失败
**解决**: 检查 `handlePromotionData` 方法的错误日志

### 问题4: 模块加载顺序问题
**症状**: 悬浮窗加载时优惠算法模块未就绪
**解决**: 使用 `waitForCalculator` 方法等待模块加载

## 测试命令

### 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到"LTBY京东助手"
3. 点击刷新按钮

### 清除缓存数据
```javascript
// 清除localStorage中的缓存数据
Object.keys(localStorage).forEach(key => {
  if (key.startsWith('jd_unified_data_')) {
    localStorage.removeItem(key);
  }
});
```

### 强制重新计算
```javascript
// 如果有悬浮窗实例，强制重新计算
if (window.jdPromotionFloatingPanel) {
  window.jdPromotionFloatingPanel.tryManualCalculation();
}
```

## 预期结果

修复后应该看到：
1. 控制台显示"设置事件监听器"和"事件监听器设置完成"
2. 悬浮窗状态从"等待中"变为"计算完成"
3. 仪表板显示正确的价格和数量信息
4. 手动计算按钮（🔧）可以正常工作

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整日志
2. 悬浮窗的当前显示状态
3. 手动测试命令的执行结果
4. 具体的商品页面URL
