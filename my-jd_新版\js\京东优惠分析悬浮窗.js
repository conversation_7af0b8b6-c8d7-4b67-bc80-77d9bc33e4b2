/**
 * 京东优惠分析悬浮窗 - 完整版
 * 集成优惠算法模块，提供完整的优惠分析功能
 * 悬浮在京东商品页面右上角
 */

(function() {
    'use strict';

    // 防止重复注入
    if (window.jdPromotionFloatingPanelFullLoaded) {
        return;
    }
    window.jdPromotionFloatingPanelFullLoaded = true;

    console.log('[京东优惠分析悬浮窗] 🚀 开始加载完整版悬浮窗');

    class JDPromotionFloatingPanel {
        constructor() {
            this.calculator = null;
            this.currentProductData = null;
            this.analysisResults = {};
            this.currentPage = 1;
            this.pageSize = 10;
            this.selectedQuantity = 1;
            this.isExpanded = false;
            this.isDragging = false;
            
            this.init();
        }

        async init() {
            console.log('[京东优惠分析悬浮窗] 🎛️ 开始初始化...');
            
            // 等待DOM加载
            await this.waitForDOM();
            
            // 注入悬浮窗
            await this.injectFloatingPanel();
            
            // 等待优惠算法模块
            await this.waitForCalculator();
            
            // 绑定事件
            this.bindEvents();
            
            // 监听优惠计算事件
            this.listenForPromotionEvents();
            
            console.log('[京东优惠分析悬浮窗] ✅ 初始化完成');
        }

        async waitForDOM() {
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }
        }

        async injectFloatingPanel() {
            try {
                console.log('[京东优惠分析悬浮窗] 🔧 开始注入悬浮窗...');

                // 检查是否已存在
                if (document.getElementById('jd-promotion-floating-panel')) {
                    console.log('[京东优惠分析悬浮窗] ℹ️ 悬浮窗已存在');
                    return;
                }

                // 检查是否在扩展环境中
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getURL) {
                    try {
                        // 获取HTML模板 - 使用URL编码的文件名
                        const templateUrl = chrome.runtime.getURL('html/%E4%BC%98%E6%83%A0%E4%BF%A1%E6%81%AF%E5%B1%95%E7%A4%BA.html');
                        console.log('[京东优惠分析悬浮窗] 🔗 模板URL:', templateUrl);

                        const response = await fetch(templateUrl);

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const htmlContent = await response.text();

                        // 创建容器并插入HTML
                        const container = document.createElement('div');
                        container.innerHTML = htmlContent;
                        document.body.appendChild(container.firstElementChild);

                        console.log('[京东优惠分析悬浮窗] ✅ 悬浮窗HTML已注入');
                        return;
                    } catch (fetchError) {
                        console.warn('[京东优惠分析悬浮窗] ⚠️ 获取HTML模板失败，使用备用方案:', fetchError);
                    }
                }

                // 备用方案：直接创建HTML结构
                console.log('[京东优惠分析悬浮窗] 使用备用HTML结构');
                this.createFallbackHTML();

            } catch (error) {
                console.error('[京东优惠分析悬浮窗] ❌ 注入悬浮窗失败:', error);
                this.createFallbackHTML();
            }
        }

        createFallbackHTML() {
            // 加载CSS样式
            this.loadCSS();

            // 创建备用的HTML结构
            const floatingPanel = document.createElement('div');
            floatingPanel.id = 'jd-promotion-floating-panel';
            floatingPanel.innerHTML = `
                <div id="dashboard-container">
                    <div class="dashboard-header">
                        <span class="dashboard-title">京东优惠分析</span>
                        <div class="dashboard-controls">
                            <span id="status-indicator" class="status-waiting">等待中</span>
                            <button id="toggle-detailed" class="btn-toggle">详细分析</button>
                        </div>
                    </div>
                    <div class="dashboard-content">
                        <div class="dashboard-row">
                            <span class="dashboard-label">最优价格:</span>
                            <span id="dashboard-optimal-price" class="dashboard-value">计算中...</span>
                        </div>
                        <div class="dashboard-row">
                            <span class="dashboard-label">最优数量:</span>
                            <span id="dashboard-optimal-quantity" class="dashboard-value">计算中...</span>
                        </div>
                        <div class="dashboard-row">
                            <span class="dashboard-label">节省金额:</span>
                            <span id="dashboard-savings" class="dashboard-value">计算中...</span>
                        </div>
                        <div class="dashboard-row">
                            <span class="dashboard-label">市斤价:</span>
                            <span id="dashboard-jin-price" class="dashboard-value">计算中...</span>
                        </div>
                        <div class="dashboard-row">
                            <span class="dashboard-label">单克价:</span>
                            <span id="dashboard-gram-price" class="dashboard-value">计算中...</span>
                        </div>
                        <div class="dashboard-actions">
                            <button id="copy-optimal" class="btn-primary">复制</button>
                            <button id="send-optimal" class="btn-secondary">发送</button>
                            <button id="add-to-cart-optimal" class="btn-success">加车</button>
                        </div>
                    </div>
                </div>
                <div id="purchase-analysis-container" style="display: none;">
                    <div class="analysis-header">
                        <h3>详细优惠分析</h3>
                        <button id="close-detailed" class="btn-close">×</button>
                    </div>
                    <div class="analysis-content">
                        <div class="unit-conversion">
                            <div id="jin-price-display">市斤价: 计算中...</div>
                            <div id="gram-price-display">单克价: 计算中...</div>
                        </div>
                        <div class="table-container">
                            <table id="analysis-table">
                                <thead>
                                    <tr>
                                        <th>数量</th>
                                        <th>单价</th>
                                        <th>总价</th>
                                        <th>优惠</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="analysis-table-body">
                                    <tr><td colspan="5">正在计算...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination">
                            <button id="prev-page" class="btn-pagination">上一页</button>
                            <span id="page-info">第 1 页</span>
                            <button id="next-page" class="btn-pagination">下一页</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(floatingPanel);
            console.log('[京东优惠分析悬浮窗] ✅ 备用HTML结构已创建');
        }

        loadCSS() {
            // 检查CSS是否已加载
            if (document.getElementById('jd-promotion-floating-panel-css')) {
                return;
            }

            // 尝试加载外部CSS文件
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getURL) {
                try {
                    const cssUrl = chrome.runtime.getURL('styles/purchase-analysis.css');
                    const link = document.createElement('link');
                    link.id = 'jd-promotion-floating-panel-css';
                    link.rel = 'stylesheet';
                    link.type = 'text/css';
                    link.href = cssUrl;
                    document.head.appendChild(link);
                    console.log('[京东优惠分析悬浮窗] ✅ CSS样式已加载');
                    return;
                } catch (error) {
                    console.warn('[京东优惠分析悬浮窗] ⚠️ 加载外部CSS失败:', error);
                }
            }

            // 备用方案：内联CSS
            const style = document.createElement('style');
            style.id = 'jd-promotion-floating-panel-css';
            style.textContent = `
                #jd-promotion-floating-panel {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    font-size: 12px;
                    width: 250px;
                    background: white;
                    border: 1px solid #007bff;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    cursor: move;
                }

                #dashboard-container {
                    background: white;
                    border-radius: 8px;
                }

                .dashboard-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background: #007bff;
                    color: white;
                    border-radius: 8px 8px 0 0;
                    font-weight: bold;
                }

                .dashboard-content {
                    padding: 8px;
                    background-color: #f8f9fa;
                    border-top: 1px solid #dee2e6;
                }

                .dashboard-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 4px;
                    font-size: 11px;
                }

                .dashboard-label {
                    color: #6c757d;
                    font-weight: normal;
                }

                .dashboard-value {
                    font-weight: bold;
                    color: #28a745;
                }

                .dashboard-actions {
                    display: flex;
                    gap: 4px;
                    margin-top: 8px;
                }

                .dashboard-actions button {
                    padding: 4px 8px;
                    font-size: 10px;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    transition: background-color 0.2s;
                }

                .btn-primary { background: #007bff; color: white; }
                .btn-secondary { background: #6c757d; color: white; }
                .btn-success { background: #28a745; color: white; }
                .btn-toggle { background: #17a2b8; color: white; }
                .btn-close { background: #dc3545; color: white; }

                .status-waiting { color: #ffc107; }
                .status-ready { color: #28a745; }
                .status-calculating { color: #17a2b8; }
                .status-complete { color: #28a745; }
                .status-error { color: #dc3545; }

                #purchase-analysis-container {
                    position: absolute;
                    top: 100%;
                    right: 0;
                    width: 450px;
                    background: white;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10001;
                }

                .analysis-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px;
                    background: #f8f9fa;
                    border-bottom: 1px solid #dee2e6;
                    border-radius: 8px 8px 0 0;
                }

                .analysis-content {
                    padding: 12px;
                }

                .table-container {
                    max-height: 300px;
                    overflow-y: auto;
                    margin: 10px 0;
                }

                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 11px;
                }

                th, td {
                    padding: 6px 8px;
                    text-align: left;
                    border-bottom: 1px solid #dee2e6;
                }

                th {
                    background: #f8f9fa;
                    font-weight: bold;
                    position: sticky;
                    top: 0;
                }

                tr:hover {
                    background: #f8f9fa;
                }

                tr.selected {
                    background: #e3f2fd;
                }

                .pagination {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    margin-top: 10px;
                }

                .btn-pagination {
                    padding: 4px 8px;
                    font-size: 10px;
                    border: 1px solid #dee2e6;
                    background: white;
                    border-radius: 3px;
                    cursor: pointer;
                }

                .btn-pagination:hover {
                    background: #f8f9fa;
                }

                .btn-pagination:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
            `;
            document.head.appendChild(style);
            console.log('[京东优惠分析悬浮窗] ✅ 内联CSS样式已加载');
        }

        async waitForCalculator() {
            console.log('[京东优惠分析悬浮窗] ⏳ 等待优惠算法模块...');

            let attempts = 0;
            const maxAttempts = 50;

            while (attempts < maxAttempts) {
                // 检查全局实例
                if (window.jdPromotionCalculator) {
                    this.calculator = window.jdPromotionCalculator;
                    this.updateStatus('模块已就绪');
                    console.log('[京东优惠分析悬浮窗] ✅ 使用全局优惠算法模块实例');
                    return;
                }

                // 检查类构造函数
                if (window.JDPromotionCalculator) {
                    try {
                        this.calculator = new window.JDPromotionCalculator();
                        this.updateStatus('模块已就绪');
                        console.log('[京东优惠分析悬浮窗] ✅ 优惠算法模块加载成功');
                        return;
                    } catch (error) {
                        console.error('[京东优惠分析悬浮窗] ❌ 模块实例化失败:', error);
                        this.updateStatus('模块错误');
                        return;
                    }
                }

                await new Promise(resolve => setTimeout(resolve, 200));
                attempts++;
            }

            console.error('[京东优惠分析悬浮窗] ❌ 优惠算法模块加载超时');
            this.updateStatus('模块加载失败');
        }

        bindEvents() {
            console.log('[京东优惠分析悬浮窗] 🔗 绑定事件监听器...');
            
            // 拖拽功能
            this.bindDragEvents();
            
            // 展开/收起功能
            this.bindToggleEvents();
            
            // 详细面板控制
            this.bindPanelEvents();
            
            // 表格交互
            this.bindTableEvents();
            
            // 复制和发送功能
            this.bindActionEvents();
        }

        bindDragEvents() {
            const header = document.querySelector('#jd-promotion-floating-panel .dashboard-header');
            const container = document.getElementById('jd-promotion-floating-panel');
            
            if (!header || !container) return;
            
            let currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;

            header.addEventListener('mousedown', (e) => {
                if (e.target.tagName === 'BUTTON') return;
                
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
                this.isDragging = true;
                header.style.cursor = 'grabbing';
            });

            document.addEventListener('mousemove', (e) => {
                if (this.isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                    xOffset = currentX;
                    yOffset = currentY;
                    
                    container.style.transform = `translate(${currentX}px, ${currentY}px)`;
                }
            });

            document.addEventListener('mouseup', () => {
                this.isDragging = false;
                header.style.cursor = 'grab';
            });
        }

        bindToggleEvents() {
            // 收起/展开仪表板
            const toggleBtn = document.getElementById('toggle-dashboard');
            const dashboardContent = document.querySelector('#jd-promotion-floating-panel .dashboard-content');
            
            if (toggleBtn && dashboardContent) {
                toggleBtn.addEventListener('click', () => {
                    const isHidden = dashboardContent.style.display === 'none';
                    dashboardContent.style.display = isHidden ? 'block' : 'none';
                    toggleBtn.textContent = isHidden ? '▼' : '▲';
                });
            }
            
            // 展开详细分析面板
            const expandBtn = document.getElementById('expand-panel-btn');
            const analysisContainer = document.getElementById('purchase-analysis-container');
            
            if (expandBtn && analysisContainer) {
                expandBtn.addEventListener('click', () => {
                    this.isExpanded = !this.isExpanded;
                    analysisContainer.style.display = this.isExpanded ? 'block' : 'none';
                    expandBtn.textContent = this.isExpanded ? '📊' : '📈';
                    
                    if (this.isExpanded && this.currentProductData) {
                        this.calculateDetailedAnalysis();
                    }
                });
            }
        }

        bindPanelEvents() {
            // 关闭详细面板
            const closeBtn = document.getElementById('close-analysis-btn');
            const analysisContainer = document.getElementById('purchase-analysis-container');
            
            if (closeBtn && analysisContainer) {
                closeBtn.addEventListener('click', () => {
                    this.isExpanded = false;
                    analysisContainer.style.display = 'none';
                    const expandBtn = document.getElementById('expand-panel-btn');
                    if (expandBtn) expandBtn.textContent = '📈';
                });
            }
            
            // 小优/大优切换
            const smallOptimalBtn = document.getElementById('show-small-optimal');
            const bigOptimalBtn = document.getElementById('show-big-optimal');
            
            if (smallOptimalBtn && bigOptimalBtn) {
                smallOptimalBtn.addEventListener('click', () => {
                    this.switchOptimalView('small');
                    smallOptimalBtn.classList.add('active');
                    bigOptimalBtn.classList.remove('active');
                });
                
                bigOptimalBtn.addEventListener('click', () => {
                    this.switchOptimalView('big');
                    bigOptimalBtn.classList.add('active');
                    smallOptimalBtn.classList.remove('active');
                });
            }
        }

        bindTableEvents() {
            // 分页控制
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            const pageSizeSelect = document.getElementById('page-size');
            
            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.updateTable();
                    }
                });
            }
            
            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    const maxPage = Math.ceil(Object.keys(this.analysisResults).length / this.pageSize);
                    if (this.currentPage < maxPage) {
                        this.currentPage++;
                        this.updateTable();
                    }
                });
            }
            
            if (pageSizeSelect) {
                pageSizeSelect.addEventListener('change', (e) => {
                    this.pageSize = parseInt(e.target.value);
                    this.currentPage = 1;
                    this.updateTable();
                });
            }
        }

        bindActionEvents() {
            // 复制选中文案
            const copyBtn = document.getElementById('copy-selected-content');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => {
                    this.copySelectedContent();
                });
            }
            
            // 发送选中文案
            const sendBtn = document.getElementById('send-selected-content');
            if (sendBtn) {
                sendBtn.addEventListener('click', () => {
                    this.sendSelectedContent();
                });
            }
            
            // 最优加车购买
            const addToCartOptimal = document.getElementById('add-to-cart-optimal');
            if (addToCartOptimal) {
                addToCartOptimal.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.addToCart(this.getOptimalQuantity());
                });
            }
        }

        listenForPromotionEvents() {
            // 监听优惠计算完成事件
            document.addEventListener('JdPromotionCalculated', (event) => {
                console.log('[京东优惠分析悬浮窗] 📊 收到优惠计算结果:', event.detail);
                this.handlePromotionData(event.detail);
            });
            
            // 监听数据库更新完成事件
            document.addEventListener('databaseFullUpdateComplete', (event) => {
                console.log('[京东优惠分析悬浮窗] 📊 收到数据库更新完成事件:', event.detail);
                // 可以在这里处理数据库相关的更新
            });
        }

        handlePromotionData(promotionData) {
            try {
                console.log('[京东优惠分析悬浮窗] 📊 处理优惠数据:', promotionData);

                this.currentProductData = promotionData.originalData;

                if (promotionData.results && promotionData.results.optimal) {
                    const optimal = promotionData.results.optimal;
                    this.updateDashboard(optimal);

                    // 存储批量计算结果
                    if (promotionData.results.batch) {
                        // 将数组转换为以数量为键的对象
                        this.analysisResults = {};
                        if (Array.isArray(promotionData.results.batch)) {
                            promotionData.results.batch.forEach(result => {
                                if (result.quantity) {
                                    this.analysisResults[result.quantity] = result;
                                }
                            });
                        } else {
                            this.analysisResults = promotionData.results.batch;
                        }

                        console.log('[京东优惠分析悬浮窗] 📊 批量结果已存储，数量范围:', Object.keys(this.analysisResults).length);
                    }

                    // 如果详细面板已展开，更新详细分析
                    if (this.isExpanded) {
                        this.updateDetailedPanel(optimal);
                        this.updateTable();
                    }
                }

                this.updateStatus('计算完成');

            } catch (error) {
                console.error('[京东优惠分析悬浮窗] 处理优惠数据失败:', error);
                this.updateStatus('数据处理失败');
            }
        }

        updateStatus(status) {
            const statusElement = document.getElementById('dashboard-status');
            if (statusElement) {
                statusElement.textContent = status;
            }
        }

        updateDashboard(optimalData) {
            // 更新仪表板显示
            const bestPriceElement = document.getElementById('dashboard-best-price');
            const bestQuantityElement = document.getElementById('dashboard-best-quantity');
            const totalPriceElement = document.getElementById('dashboard-total-price');
            
            if (bestPriceElement && optimalData.bestUnitPrice) {
                bestPriceElement.textContent = `¥${optimalData.bestUnitPrice.toFixed(2)}`;
                bestPriceElement.style.color = '#28a745';
            }
            
            if (bestQuantityElement && optimalData.bestQuantity) {
                bestQuantityElement.textContent = `×${optimalData.bestQuantity}`;
                bestQuantityElement.style.color = '#28a745';
            }
            
            if (totalPriceElement && optimalData.bestTotalPrice) {
                totalPriceElement.textContent = `¥${optimalData.bestTotalPrice.toFixed(2)}`;
                totalPriceElement.style.color = '#28a745';
            }
            
            // 更新单位换算信息
            this.updateUnitConversion(optimalData);
        }

        updateUnitConversion(optimalData) {
            // 根据商品标题和最优价格计算单位换算
            const jinPriceElement = document.getElementById('dashboard-jin-price');
            const gramPriceElement = document.getElementById('dashboard-gram-price');

            if (jinPriceElement && optimalData && optimalData.bestUnitPrice) {
                // 尝试解析商品标题中的重量信息
                const weightInfo = this.parseWeightFromTitle();
                if (weightInfo) {
                    const jinPrice = this.calculateJinPrice(optimalData.bestUnitPrice, weightInfo);
                    jinPriceElement.textContent = jinPrice ? `¥${jinPrice.toFixed(2)}/市斤` : '无法计算';
                    jinPriceElement.style.color = jinPrice ? '#28a745' : '#6c757d';
                } else {
                    jinPriceElement.textContent = '无重量信息';
                    jinPriceElement.style.color = '#6c757d';
                }
            }

            if (gramPriceElement && optimalData && optimalData.bestUnitPrice) {
                const weightInfo = this.parseWeightFromTitle();
                if (weightInfo) {
                    const gramPrice = this.calculateGramPrice(optimalData.bestUnitPrice, weightInfo);
                    gramPriceElement.textContent = gramPrice ? `¥${gramPrice.toFixed(3)}/克` : '无法计算';
                    gramPriceElement.style.color = gramPrice ? '#28a745' : '#6c757d';
                } else {
                    gramPriceElement.textContent = '无重量信息';
                    gramPriceElement.style.color = '#6c757d';
                }
            }
        }

        calculateDetailedAnalysis() {
            if (!this.calculator || !this.currentProductData) {
                console.warn('[京东优惠分析悬浮窗] 无法进行详细分析：缺少计算器或商品数据');
                return;
            }

            try {
                this.updateStatus('详细计算中...');

                // 如果已有批量计算结果，直接使用
                if (Object.keys(this.analysisResults).length > 0) {
                    console.log('[京东优惠分析悬浮窗] 使用已有的批量计算结果');
                    this.updateTable();
                    this.updateStatus('详细分析完成');
                    return;
                }

                // 使用优惠算法模块的批量计算方法
                const batchResults = this.calculator.calculateBatchPromotions(this.currentProductData, 200);

                if (batchResults && batchResults.batchResults) {
                    // 将数组转换为以数量为键的对象
                    this.analysisResults = {};
                    if (Array.isArray(batchResults.batchResults)) {
                        batchResults.batchResults.forEach(result => {
                            if (result.quantity) {
                                this.analysisResults[result.quantity] = result;
                            }
                        });
                    }

                    console.log('[京东优惠分析悬浮窗] 批量计算完成，数量范围:', Object.keys(this.analysisResults).length);
                }

                this.updateTable();
                this.updateStatus('详细分析完成');

            } catch (error) {
                console.error('[京东优惠分析悬浮窗] 详细分析失败:', error);
                this.updateStatus('详细分析失败');
            }
        }

        updateDetailedPanel(optimalData) {
            // 更新详细面板的汇总信息
            const bestPriceElement = document.getElementById('best-price');
            const originalPriceElement = document.getElementById('original-price');
            const bestQuantityElement = document.getElementById('best-quantity');
            const totalBestPriceElement = document.getElementById('total-best-price');
            const totalOriginalPriceElement = document.getElementById('total-original-price');

            if (bestPriceElement && optimalData.bestUnitPrice) {
                bestPriceElement.textContent = `¥${optimalData.bestUnitPrice.toFixed(2)}`;
            }

            if (originalPriceElement && this.currentProductData && this.currentProductData.price) {
                const originalPrice = this.extractOriginalPrice();
                originalPriceElement.textContent = `¥${originalPrice.toFixed(2)}`;
            }

            if (bestQuantityElement && optimalData.bestQuantity) {
                bestQuantityElement.textContent = `×${optimalData.bestQuantity}`;
            }

            if (totalBestPriceElement && optimalData.bestTotalPrice) {
                totalBestPriceElement.textContent = `¥${optimalData.bestTotalPrice.toFixed(2)}`;
            }

            if (totalOriginalPriceElement && optimalData.bestQuantity) {
                const originalPrice = this.extractOriginalPrice();
                const totalOriginal = originalPrice * optimalData.bestQuantity;
                totalOriginalPriceElement.textContent = `¥${totalOriginal.toFixed(2)}`;
            }

            // 更新单位换算显示
            this.updateUnitConversionDisplay(optimalData);

            // 更新优惠信息列表
            this.updatePromotionsList(optimalData);
        }

        updateUnitConversionDisplay(optimalData) {
            const jinPriceDisplay = document.getElementById('jin-price-display');
            const gramPriceDisplay = document.getElementById('gram-price-display');

            if (jinPriceDisplay && optimalData && optimalData.bestUnitPrice) {
                const weightInfo = this.parseWeightFromTitle();
                if (weightInfo) {
                    const jinPrice = this.calculateJinPrice(optimalData.bestUnitPrice, weightInfo);
                    jinPriceDisplay.textContent = jinPrice ? `市斤价: ¥${jinPrice.toFixed(2)}` : '市斤价: 无法计算';
                } else {
                    jinPriceDisplay.textContent = '市斤价: 无重量信息';
                }
            }

            if (gramPriceDisplay && optimalData && optimalData.bestUnitPrice) {
                const weightInfo = this.parseWeightFromTitle();
                if (weightInfo) {
                    const gramPrice = this.calculateGramPrice(optimalData.bestUnitPrice, weightInfo);
                    gramPriceDisplay.textContent = gramPrice ? `单克价: ¥${gramPrice.toFixed(3)}` : '单克价: 无法计算';
                } else {
                    gramPriceDisplay.textContent = '单克价: 无重量信息';
                }
            }
        }

        updatePromotionsList(optimalData) {
            const promotionList = document.getElementById('promotion-list');
            if (!promotionList) return;

            // 清空现有内容
            promotionList.innerHTML = '';

            if (!optimalData.appliedPromotions || optimalData.appliedPromotions.length === 0) {
                promotionList.innerHTML = '<li style="margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px;"><div style="color: #666;">暂无可用优惠</div></li>';
                return;
            }

            // 渲染优惠信息
            optimalData.appliedPromotions.forEach(promotion => {
                const li = document.createElement('li');
                li.style.cssText = 'margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px; border-left: 3px solid #007bff;';

                const discountAmount = promotion.discountAmount || 0;
                const promotionText = promotion.text || '未知优惠';
                const promotionType = this.getPromotionType(promotion);

                li.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                        <span style="font-weight: bold; color: #dc3545;">减${discountAmount.toFixed(2)}元</span>
                        <span style="color: #007bff; font-weight: bold;">${promotionType}</span>
                    </div>
                    <div style="color: #666; margin-bottom: 6px;">${promotionText}</div>
                    <div style="display: flex; gap: 5px;">
                        ${this.generatePromotionButtons(promotion)}
                    </div>
                `;

                promotionList.appendChild(li);
            });
        }

        getPromotionType(promotion) {
            if (promotion.type === 'coupon') {
                return '优惠券';
            } else if (promotion.type === 'promotion') {
                if (promotion.text && promotion.text.includes('PLUS')) {
                    return 'PLUS专享';
                } else if (promotion.text && promotion.text.includes('多买')) {
                    return '多买优惠';
                } else if (promotion.text && promotion.text.includes('满减')) {
                    return '满减优惠';
                } else {
                    return '促销优惠';
                }
            }
            return '优惠';
        }

        generatePromotionButtons(promotion) {
            let buttons = '';

            // 如果是优惠券，添加领取按钮
            if (promotion.type === 'coupon' && promotion.encryptedKey && promotion.roleId) {
                const couponUrl = `https://coupon.m.jd.com/coupons/show.action?key=${promotion.encryptedKey}&roleId=${promotion.roleId}&to=m.jd.com`;
                buttons += `<button onclick="window.open('${couponUrl}', '_blank')" style="padding: 2px 6px; font-size: 10px; background: #28a745; color: white; border: none; border-radius: 2px; cursor: pointer;">触屏领取</button>`;
            }

            // 如果有凑单链接，添加凑单按钮
            if (promotion.toUrl) {
                buttons += `<button onclick="window.open('${promotion.toUrl}', '_blank')" style="padding: 2px 6px; font-size: 10px; background: #17a2b8; color: white; border: none; border-radius: 2px; cursor: pointer; margin-left: 3px;">去凑单</button>`;
            }

            // 添加同品牌凑单按钮（需要获取品牌信息）
            buttons += `<button onclick="this.openBrandSearch()" style="padding: 2px 6px; font-size: 10px; background: #6f42c1; color: white; border: none; border-radius: 2px; cursor: pointer; margin-left: 3px;">同品牌凑单</button>`;

            // 添加忽略按钮
            buttons += `<button style="padding: 2px 6px; font-size: 10px; background: #6c757d; color: white; border: none; border-radius: 2px; cursor: pointer; margin-left: 3px;">[忽略]</button>`;

            return buttons;
        }

        updateTable() {
            const tableBody = document.getElementById('analysis-table-body');
            if (!tableBody) return;

            // 清空现有内容
            tableBody.innerHTML = '';

            if (Object.keys(this.analysisResults).length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" style="border: 1px solid #ddd; padding: 8px; text-align: center; color: #666;">
                            暂无分析数据，请等待计算完成
                        </td>
                    </tr>
                `;
                return;
            }

            // 获取当前页的数据
            const quantities = Object.keys(this.analysisResults).map(Number).sort((a, b) => a - b);
            const startIndex = (this.currentPage - 1) * this.pageSize;
            const endIndex = startIndex + this.pageSize;
            const currentPageQuantities = quantities.slice(startIndex, endIndex);

            // 渲染表格行
            currentPageQuantities.forEach(quantity => {
                const result = this.analysisResults[quantity];
                if (!result) return;

                const tr = document.createElement('tr');

                const isOptimal = this.isOptimalQuantity(quantity);
                const isSubOptimal = this.isSubOptimalQuantity(quantity);

                tr.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">
                        <input type="radio" name="quantity-selection" value="${quantity}" style="margin-right: 5px;" ${quantity === this.selectedQuantity ? 'checked' : ''}> ${quantity}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 8px;">¥${result.finalUnitPrice.toFixed(2)}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">¥${result.finalPrice.toFixed(2)}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        ${isOptimal ? '<button class="optimal" style="background-color: #ff4400; color: white; border: none; padding: 2px 5px; font-size: 10px; margin-right: 5px;">大优</button>' : ''}
                        ${isSubOptimal ? '<button class="sub-optimal" style="background-color: #ff8c00; color: white; border: none; padding: 2px 5px; font-size: 10px; margin-right: 5px;">小优</button>' : ''}
                        <a href="#" class="add-to-cart-btn" data-quantity="${quantity}" style="font-size: 11px; text-decoration: none; color: #007bff;">加车购买</a>
                    </td>
                `;

                tableBody.appendChild(tr);
            });

            // 绑定新的事件监听器
            this.bindTableRowEvents();

            // 更新分页信息
            this.updatePaginationInfo(quantities.length);
        }

        bindTableRowEvents() {
            // 绑定数量选择事件
            const radioButtons = document.querySelectorAll('input[name="quantity-selection"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', (e) => {
                    this.selectedQuantity = parseInt(e.target.value);
                    console.log('[京东优惠分析悬浮窗] 选中数量:', this.selectedQuantity);
                });
            });

            // 绑定加车购买事件
            const addToCartBtns = document.querySelectorAll('.add-to-cart-btn');
            addToCartBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const quantity = parseInt(btn.getAttribute('data-quantity'));
                    this.addToCart(quantity);
                });
            });
        }

        updatePaginationInfo(totalItems) {
            const pageInfo = document.getElementById('page-info');
            if (pageInfo) {
                const maxPage = Math.ceil(totalItems / this.pageSize);
                pageInfo.textContent = `第 ${this.currentPage} 页 / 共 ${maxPage} 页`;
            }
        }

        isOptimalQuantity(quantity) {
            // 判断是否为最优数量（大优）
            if (!this.analysisResults || Object.keys(this.analysisResults).length === 0) return false;

            let bestUnitPrice = Infinity;
            let bestQuantity = 1;

            for (const [qty, result] of Object.entries(this.analysisResults)) {
                if (result.finalUnitPrice < bestUnitPrice) {
                    bestUnitPrice = result.finalUnitPrice;
                    bestQuantity = parseInt(qty);
                }
            }

            return quantity === bestQuantity;
        }

        isSubOptimalQuantity(quantity) {
            // 判断是否为次优数量（小优）
            if (!this.analysisResults || Object.keys(this.analysisResults).length === 0) return false;

            const results = Object.entries(this.analysisResults)
                .map(([qty, result]) => ({ quantity: parseInt(qty), unitPrice: result.finalUnitPrice }))
                .sort((a, b) => a.unitPrice - b.unitPrice);

            // 取前3名作为小优
            const top3 = results.slice(0, 3);
            return top3.some(item => item.quantity === quantity) && !this.isOptimalQuantity(quantity);
        }

        extractOriginalPrice() {
            if (!this.currentProductData || !this.currentProductData.price) return 0;

            // 尝试从不同的价格字段提取原价
            if (this.currentProductData.price.p) {
                return parseFloat(this.currentProductData.price.p);
            } else if (this.currentProductData.price.op) {
                return parseFloat(this.currentProductData.price.op);
            } else if (this.currentProductData.price.m) {
                return parseFloat(this.currentProductData.price.m);
            }

            return 0;
        }

        getOptimalQuantity() {
            if (!this.analysisResults || Object.keys(this.analysisResults).length === 0) return 1;

            let bestUnitPrice = Infinity;
            let bestQuantity = 1;

            for (const [quantity, result] of Object.entries(this.analysisResults)) {
                if (result.finalUnitPrice < bestUnitPrice) {
                    bestUnitPrice = result.finalUnitPrice;
                    bestQuantity = parseInt(quantity);
                }
            }

            return bestQuantity;
        }

        switchOptimalView(viewType) {
            // 切换小优/大优视图
            console.log('[京东优惠分析悬浮窗] 切换视图:', viewType);
            // 这里可以添加不同视图的逻辑
            this.updateTable();
        }

        async copySelectedContent() {
            try {
                // 优先使用现有的格式化文案模块
                if (window.FormattedCopyManager && window.FormattedCopyManager.initialized) {
                    const result = await window.FormattedCopyManager.copyFormattedContent();
                    if (result.success) {
                        this.showTip(result.message);
                        console.log('[京东优惠分析悬浮窗] 使用格式化文案模块复制成功:', result.content);
                        return;
                    } else {
                        console.warn('[京东优惠分析悬浮窗] 格式化文案模块复制失败，使用备用方案');
                    }
                }

                // 备用方案：使用自定义格式化
                const quantity = this.selectedQuantity;
                const analysisResult = this.analysisResults[quantity];

                if (!analysisResult) {
                    console.warn('[京东优惠分析悬浮窗] 未找到选中数量的计算结果');
                    this.showTip('未找到计算结果');
                    return;
                }

                // 生成格式化文案
                const content = this.generateFormattedContent(quantity, analysisResult);

                await navigator.clipboard.writeText(content);
                console.log('[京东优惠分析悬浮窗] 已复制格式化文案:', content);

                // 显示提示
                this.showTip('已复制选中数量的文案');

            } catch (error) {
                console.error('[京东优惠分析悬浮窗] 复制失败:', error);
                this.showTip('复制失败: ' + error.message);
            }
        }

        async sendSelectedContent() {
            try {
                // 获取格式化文案
                let content = '';

                // 优先使用现有的格式化文案模块
                if (window.FormattedCopyManager && window.FormattedCopyManager.initialized) {
                    content = window.FormattedCopyManager.generateFormattedContent();
                    if (!content) {
                        console.warn('[京东优惠分析悬浮窗] 格式化文案模块生成失败，使用备用方案');
                    }
                }

                // 备用方案：使用自定义格式化
                if (!content) {
                    const quantity = this.selectedQuantity;
                    const analysisResult = this.analysisResults[quantity];

                    if (!analysisResult) {
                        console.warn('[京东优惠分析悬浮窗] 未找到选中数量的计算结果');
                        this.showTip('未找到计算结果');
                        return;
                    }

                    content = this.generateFormattedContent(quantity, analysisResult);
                }

                // 使用WebSocket发送
                if (window.middlewareWebSocketSender) {
                    const sendResult = await window.middlewareWebSocketSender.sendFormattedContent(content, '');
                    if (sendResult && sendResult.success) {
                        this.showTip('发送成功！');
                        console.log('[京东优惠分析悬浮窗] WebSocket发送成功');
                    } else {
                        this.showTip('发送失败');
                        console.warn('[京东优惠分析悬浮窗] WebSocket发送失败:', sendResult);
                    }
                } else {
                    console.error('[京东优惠分析悬浮窗] WebSocket发送模块未加载');
                    this.showTip('发送模块未加载');
                }

            } catch (error) {
                console.error('[京东优惠分析悬浮窗] 发送失败:', error);
                this.showTip('发送失败: ' + error.message);
            }
        }

        generateFormattedContent(quantity, result) {
            // 生成格式化文案，参考复制格式化文案模块的逻辑
            const title = this.getProductTitle();
            const convertedLink = window.convertedLink || window.location.href;
            const originalPrice = this.extractOriginalPrice();
            const finalUnitPrice = result.finalUnitPrice;
            const finalTotalPrice = result.finalPrice;
            const savings = (originalPrice * quantity) - finalTotalPrice;

            let content = '';

            // 计算折扣
            const discount = originalPrice > 0 ? (finalUnitPrice / originalPrice * 10).toFixed(1) : '特价';

            // 标题行
            if (discount === '特价' || Math.abs(originalPrice - finalUnitPrice) < 0.01) {
                content += `✅【特价】${title}\n\n`;
            } else {
                content += `✅【${discount}折】${title}\n\n`;
            }

            // 链接行
            content += `👉${convertedLink}\n`;
            content += `----------------\n`;

            // 价格提示
            content += `❗价格不对，可能地区价，或优惠已结束\n`;

            if (discount === '特价') {
                content += `👉购买${quantity}件最优\n`;
                content += `✅到手单价：${finalUnitPrice.toFixed(2)}`;
            } else {
                content += `👉商品原价：${originalPrice.toFixed(2)}\n`;

                if (savings > 0.01) {
                    content += `👉购买${quantity}件最优，省${savings.toFixed(1)}\n`;
                } else {
                    content += `👉购买${quantity}件最优\n`;
                }

                content += `👇参与优惠后\n`;
                content += `✅到手总价：${finalTotalPrice.toFixed(2)}\n`;
                content += `✅到手单价：${finalUnitPrice.toFixed(2)}`;
            }

            return content;
        }

        getProductTitle() {
            // 尝试从页面获取商品标题
            const titleSelectors = [
                '.sku-name',
                '.itemInfo-wrap .sku-name',
                '.product-intro .sku-name',
                'h1'
            ];

            for (const selector of titleSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    return element.textContent.trim();
                }
            }

            return '京东商品';
        }

        addToCart(quantity) {
            console.log('[京东优惠分析悬浮窗] 加车购买，数量:', quantity);

            try {
                // 集成现有的一键购买模块
                if (window.ltbyQuickBuy && typeof window.ltbyQuickBuy.openBuyNow === 'function') {
                    // 使用一键购买模块的购买功能
                    window.ltbyQuickBuy.openBuyNow(quantity);
                    this.showTip(`正在购买 ${quantity} 件商品`);
                    console.log('[京东优惠分析悬浮窗] 使用一键购买模块进行购买');
                } else if (window.ltbyQuickBuy && typeof window.ltbyQuickBuy.performQuickBuy === 'function') {
                    // 备用方案：使用兼容性方法
                    // 先设置数量
                    const quantityInput = document.querySelector('#quantity-input, .quantity-form input, input[name="quantity"]');
                    if (quantityInput) {
                        quantityInput.value = quantity;
                        // 触发change事件
                        quantityInput.dispatchEvent(new Event('change', { bubbles: true }));
                    }

                    window.ltbyQuickBuy.performQuickBuy();
                    this.showTip(`正在购买 ${quantity} 件商品`);
                    console.log('[京东优惠分析悬浮窗] 使用一键购买模块兼容方法进行购买');
                } else {
                    // 手动实现基本的加车功能
                    const productId = this.getProductId();
                    if (productId) {
                        const timestamp = Date.now();
                        const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;

                        // 使用Image对象发送请求
                        const img = new Image();
                        img.onload = img.onerror = () => {
                            console.log('[京东优惠分析悬浮窗] 加入购物车请求已发送');
                        };
                        img.src = addToCartUrl;

                        this.showTip(`已加车 ${quantity} 件商品`);
                        console.log('[京东优惠分析悬浮窗] 使用手动方式加入购物车');
                    } else {
                        this.showTip('无法获取商品ID');
                        console.error('[京东优惠分析悬浮窗] 无法获取商品ID');
                    }
                }
            } catch (error) {
                console.error('[京东优惠分析悬浮窗] 加车失败:', error);
                this.showTip('加车失败: ' + error.message);
            }
        }

        showTip(message) {
            // 显示提示信息
            const tip = document.createElement('div');
            tip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 10001;
                pointer-events: none;
            `;
            tip.textContent = message;

            document.body.appendChild(tip);
            setTimeout(() => tip.remove(), 2000);
        }

        // 重量解析和单位换算辅助方法
        parseWeightFromTitle() {
            const title = this.getProductTitle();
            if (!title) return null;

            // 常见重量单位正则表达式
            const weightPatterns = [
                /(\d+(?:\.\d+)?)\s*[克g]/i,           // 克
                /(\d+(?:\.\d+)?)\s*[千公]?[克g]/i,    // 千克/公克
                /(\d+(?:\.\d+)?)\s*kg/i,             // kg
                /(\d+(?:\.\d+)?)\s*斤/i,             // 斤
                /(\d+(?:\.\d+)?)\s*[磅lb]/i,         // 磅
                /(\d+(?:\.\d+)?)\s*[两]/i,           // 两
            ];

            for (const pattern of weightPatterns) {
                const match = title.match(pattern);
                if (match) {
                    const value = parseFloat(match[1]);
                    const unit = match[0].replace(match[1], '').trim().toLowerCase();

                    return {
                        value: value,
                        unit: unit,
                        originalText: match[0]
                    };
                }
            }

            return null;
        }

        calculateJinPrice(unitPrice, weightInfo) {
            if (!weightInfo || !unitPrice) return null;

            const { value, unit } = weightInfo;

            // 转换为市斤价格（1市斤 = 500克）
            let gramsPerUnit = 0;

            switch (unit) {
                case 'g':
                case '克':
                    gramsPerUnit = value;
                    break;
                case 'kg':
                case '千克':
                case '公斤':
                    gramsPerUnit = value * 1000;
                    break;
                case '斤':
                    gramsPerUnit = value * 500; // 1斤 = 500克
                    break;
                case '两':
                    gramsPerUnit = value * 50;  // 1两 = 50克
                    break;
                case 'lb':
                case '磅':
                    gramsPerUnit = value * 453.592; // 1磅 = 453.592克
                    break;
                default:
                    return null;
            }

            if (gramsPerUnit <= 0) return null;

            // 计算每市斤的价格
            const jinPrice = (unitPrice / gramsPerUnit) * 500;
            return jinPrice;
        }

        calculateGramPrice(unitPrice, weightInfo) {
            if (!weightInfo || !unitPrice) return null;

            const { value, unit } = weightInfo;

            // 转换为每克价格
            let gramsPerUnit = 0;

            switch (unit) {
                case 'g':
                case '克':
                    gramsPerUnit = value;
                    break;
                case 'kg':
                case '千克':
                case '公斤':
                    gramsPerUnit = value * 1000;
                    break;
                case '斤':
                    gramsPerUnit = value * 500;
                    break;
                case '两':
                    gramsPerUnit = value * 50;
                    break;
                case 'lb':
                case '磅':
                    gramsPerUnit = value * 453.592;
                    break;
                default:
                    return null;
            }

            if (gramsPerUnit <= 0) return null;

            // 计算每克的价格
            const gramPrice = unitPrice / gramsPerUnit;
            return gramPrice;
        }

        getProductId() {
            // 从URL中提取商品ID
            const url = window.location.href;

            // 匹配京东商品页面的各种URL格式
            const patterns = [
                /\/(\d+)\.html/,           // 标准格式：/12345.html
                /item\.jd\.com\/(\d+)/,    // item.jd.com/12345
                /product\/(\d+)/,          // product/12345
                /skuid[=:](\d+)/i,         // skuid=12345 或 skuid:12345
                /pid[=:](\d+)/i,           // pid=12345 或 pid:12345
                /id[=:](\d+)/i             // id=12345 或 id:12345
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    console.log('[京东优惠分析悬浮窗] 提取到商品ID:', match[1]);
                    return match[1];
                }
            }

            // 从页面元素中提取
            const selectors = [
                '[data-sku]',
                '[data-product-id]',
                '[data-pid]',
                '#InitCartUrl',
                '.product-intro'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    const sku = element.getAttribute('data-sku') ||
                               element.getAttribute('data-product-id') ||
                               element.getAttribute('data-pid');
                    if (sku) {
                        console.log('[京东优惠分析悬浮窗] 从元素提取到商品ID:', sku);
                        return sku;
                    }
                }
            }

            console.warn('[京东优惠分析悬浮窗] 无法提取商品ID');
            return null;
        }
    }

    // 初始化函数
    async function initJDPromotionFloatingPanel() {
        try {
            console.log('[京东优惠分析悬浮窗] 🚀 开始初始化完整版悬浮窗...');

            // 检查页面
            if (!window.location.href.includes('item.jd.com') &&
                !window.location.href.includes('item.jd.hk')) {
                console.log('[京东优惠分析悬浮窗] ℹ️ 非京东商品页面');
                return;
            }

            // 等待一段时间确保其他模块加载完成
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 创建悬浮窗实例
            window.jdPromotionFloatingPanel = new JDPromotionFloatingPanel();

            console.log('[京东优惠分析悬浮窗] 🎉 完整版悬浮窗初始化完成');

        } catch (error) {
            console.error('[京东优惠分析悬浮窗] ❌ 初始化失败:', error);
        }
    }

    // 启动
    initJDPromotionFloatingPanel();

})();
